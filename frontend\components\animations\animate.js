"use client"

import { useEffect, useRef } from "react"
import { motion, useAnimation, useInView } from "framer-motion"

export function Animate({
  children,
  type = "fade",
  delay = 0,
  duration = 0.3,
  className = "",
  threshold = 0.1,
  once = true,
}) {
  const controls = useAnimation()
  const ref = useRef(null)
  const inView = useInView(ref, { threshold, once })

  useEffect(() => {
    if (inView) {
      controls.start("visible")
    } else if (!once) {
      controls.start("hidden")
    }
  }, [controls, inView, once])

  const variants = {
    fade: {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration, delay } },
    },
    slide: {
      hidden: { opacity: 0, y: 10 },
      visible: { opacity: 1, y: 0, transition: { duration, delay } },
    },
    scale: {
      hidden: { opacity: 0, scale: 0.95 },
      visible: { opacity: 1, scale: 1, transition: { duration, delay } },
    },
    none: {
      hidden: {},
      visible: {},
    },
  }

  if (type === "none") {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div ref={ref} initial="hidden" animate={controls} variants={variants[type]} className={className}>
      {children}
    </motion.div>
  )
}

export function AnimatePresence({ children }) {
  return <>{children}</>
}

export function useRevealAnimation() {
  useEffect(() => {
    const revealElements = document.querySelectorAll(".reveal")

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("active")
          }
        })
      },
      { threshold: 0.1 },
    )

    revealElements.forEach((element) => {
      observer.observe(element)
    })

    return () => {
      revealElements.forEach((element) => {
        observer.unobserve(element)
      })
    }
  }, [])
}
