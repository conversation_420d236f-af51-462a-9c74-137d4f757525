# import fitz
# pymupdf相关问题请看：https://pymupdf.readthedocs.io/en/latest/installation.html
import pymupdf as fitz
import cv2
import numpy as np
import time
import os
import glob # 用于查找文件
from pydub import AudioSegment # 用于处理音频
import subprocess # 用于调用FFmpeg
import re # 用于排序文件名中的数字
import logging


# 抑制pydub和FFmpeg的调试日志
logging.getLogger("pydub").setLevel(logging.ERROR)
logging.getLogger("pydub.converter").setLevel(logging.ERROR)
logging.getLogger("pydub.utils").setLevel(logging.ERROR)
logging.getLogger("pydub.silence").setLevel(logging.ERROR)
logging.getLogger("pydub.playback").setLevel(logging.ERROR)

# 设置FFmpeg日志级别环境变量
os.environ['FFMPEG_LOGLEVEL'] = 'error'
# 移除FFREPORT设置，因为它会导致"Invalid report file level"错误

# 配置pydub使用静默模式
from pydub.utils import which
from pydub import AudioSegment
# 设置pydub的FFmpeg路径和参数
AudioSegment.converter = which("ffmpeg")
AudioSegment.ffmpeg = which("ffmpeg")
AudioSegment.ffprobe = which("ffprobe")


# 配置日志
logger = logging.getLogger(__name__)

def generate_video_from_pdf_and_audio(
    pdf_path,
    audio_folder,
    output_video_path=None,
    rendering_dpi=200,
    fps=2,  # 降低FPS以加快处理速度，对静态PPT影响很小
    clean_temp_files=True
):
    """
    从PDF文件和音频文件生成视频

    参数:
    pdf_path (str): PDF文件路径
    audio_folder (str): 存放音频文件的文件夹路径
    output_video_path (str, optional): 输出视频文件路径，如果不指定则使用默认名称
    rendering_dpi (int, optional): PDF渲染分辨率
    fps (int, optional): 输出视频帧率
    clean_temp_files (bool, optional): 是否清理临时文件

    返回:
    str: 生成的视频文件路径，如果失败则返回None
    """
    # 设置默认输出视频路径
    if output_video_path is None:
        output_video_filename = f"presentation_with_audio_{int(time.time())}.mp4"
        output_video_path = os.path.join(os.path.dirname(pdf_path), output_video_filename)

    # 确保输出路径是绝对路径
    output_video_path = os.path.abspath(output_video_path)

    # 临时文件路径 - 使用绝对路径
    temp_dir = os.path.dirname(output_video_path)
    temp_video_path = os.path.join(temp_dir, f"temp_video_no_audio_{int(time.time())}.mp4")
    temp_audio_path = os.path.join(temp_dir, f"temp_concatenated_audio_{int(time.time())}.mp3")

    logger.info(f"输出视频路径: {output_video_path}")
    logger.info(f"临时视频路径: {temp_video_path}")
    logger.info(f"临时音频路径: {temp_audio_path}")

    # 确保输入文件和文件夹存在
    if not os.path.exists(pdf_path):
        logger.error(f"错误: 输入PDF文件不存在 - {pdf_path}")
        return None
    if not os.path.exists(audio_folder):
        logger.error(f"错误: 音频文件夹不存在 - {audio_folder}")
        return None

    # 检查FFmpeg是否可用（测试期间跳过）
    # TODO: 测试期间跳过FFmpeg检查 - 如果MiniMax API优化成功，FFmpeg主要用于最终视频合并
    try:
        subprocess.run(['ffmpeg', '-loglevel', 'error', '-hide_banner', '-version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        logger.info("FFmpeg 检查通过。")
    except FileNotFoundError:
        logger.warning("FFmpeg 未找到在PATH中，但继续处理（测试模式）。")
        logger.info("尝试检查常见的FFmpeg安装位置...")
        # 检查常见的FFmpeg位置
        common_paths = ['/usr/bin/ffmpeg', '/usr/local/bin/ffmpeg', '/opt/ffmpeg/bin/ffmpeg']
        for path in common_paths:
            if os.path.exists(path):
                logger.info(f"找到FFmpeg在: {path}")
                break
        else:
            logger.warning("在常见位置未找到FFmpeg")
        # 不返回None，继续处理
        pass
    except subprocess.CalledProcessError as e:
        logger.warning(f"FFmpeg 执行失败，但继续处理（测试模式）。错误: {e}")
        # 不返回None，继续处理
        pass


    logger.info(f"正在处理PDF文件: {pdf_path}")

    doc = None # 初始化PDF文档对象
    out = None # 初始化视频写入器对象

    try:
        # 打开PDF文件
        doc = fitz.open(pdf_path)
        num_pages = doc.page_count
        logger.info(f"PDF总页数: {num_pages}")

        if num_pages == 0:
            logger.error("错误: PDF文件不包含任何页面。")
            return None

        # --- 处理音频文件 ---
        logger.info(f"正在扫描音频文件夹: {audio_folder}")
        # 查找所有MP3文件，并按页码排序
        # 使用 glob 查找文件，然后用自定义排序函数按数字排序
        audio_files = glob.glob(os.path.join(audio_folder, "slide*.mp3"))

        # 如果没有找到文件，尝试其他可能的模式
        if not audio_files:
            logger.info("未找到slide*.mp3格式的文件，尝试查找其他格式...")
            audio_files = glob.glob(os.path.join(audio_folder, "*.mp3"))

        # 提取文件名中的数字并进行排序
        def natural_sort_key(s):
            # 提取文件名中的数字部分
            filename = os.path.basename(s)
            # 使用正则表达式提取数字
            match = re.search(r'(\d+)', filename)
            if match:
                return int(match.group(1))
            return 0  # 如果没有数字，默认为0

        # 按数字排序
        audio_files = sorted(audio_files, key=natural_sort_key)

        # 检查音频文件数量是否与PDF页数匹配
        if len(audio_files) != num_pages:
            logger.error(f"警告: 音频文件数量 ({len(audio_files)}) 与PDF页数 ({num_pages}) 不匹配。")
            logger.error(f"找到的音频文件: {[os.path.basename(f) for f in audio_files]}")
            return None

        logger.info(f"找到并按页码排序的音频文件 ({len(audio_files)}):")
        for af in audio_files:
            logger.info(f"{os.path.basename(af)}")

        # 读取每个音频文件的时长并存储
        page_durations_ms = []
        concatenated_audio = AudioSegment.empty()

        logger.info("正在读取音频时长并拼接...")
        for i, audio_file in enumerate(audio_files):
            try:
                # 检查音频文件是否存在和大小
                logger.info(f"读取音频文件: {audio_file}")
                if not os.path.exists(audio_file):
                    logger.error(f"音频文件不存在: {audio_file}")
                    return None

                file_size = os.path.getsize(audio_file)
                logger.info(f"音频文件大小: {file_size} 字节")

                if file_size == 0:
                    logger.error(f"音频文件为空: {audio_file}")
                    return None

                # 简单检查文件内容（MiniMax生成的文件通常是可用的）
                logger.info(f"读取音频文件: {os.path.basename(audio_file)}")

                # 使用pydub读取音频
                audio = AudioSegment.from_file(audio_file)
                page_durations_ms.append(len(audio)) # 时长以毫秒为单位
                concatenated_audio += audio # 拼接音频
                logger.info(f"  - 读取 {os.path.basename(audio_file)}, 时长: {len(audio)/1000:.2f} 秒")
            except Exception as e:
                logger.error(f"错误: 无法处理音频文件 {audio_file}: {e}")
                return None

        # 将拼接好的音频导出为临时文件
        logger.info(f"导出拼接音频到临时文件: {temp_audio_path}")
        concatenated_audio.export(temp_audio_path, format="mp3") # 可以选择其他格式如 "aac"

        # --- 生成视频帧 ---

        # 渲染第一页以获取帧的尺寸，用于初始化视频写入器
        logger.info("渲染第一页以确定视频尺寸...")
        first_page = doc.load_page(0)
        # 使用白色背景渲染PDF
        pix = first_page.get_pixmap(dpi=rendering_dpi, alpha=False)

        # 获取渲染图片的尺寸 (宽度, 高度) for OpenCV
        frame_width = pix.width
        frame_height = pix.height
        frame_size = (frame_width, frame_height)
        logger.info(f"渲染尺寸: {frame_size} 像素 ({rendering_dpi} DPI)")

        # 初始化视频写入器 - 生成不含音频的临时视频文件
        # 尝试不同的编码器，确保兼容性
        try:
            # 首先尝试 XVID 编码器，这是一个广泛支持的编码器
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            out = cv2.VideoWriter(temp_video_path, fourcc, fps, frame_size)

            # 检查是否成功打开
            if not out.isOpened():
                # 如果失败，尝试 mp4v 编码器
                logger.info("XVID 编码器初始化失败，尝试 mp4v 编码器...")
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                out = cv2.VideoWriter(temp_video_path, fourcc, fps, frame_size)

                # 如果仍然失败，尝试 MJPG 编码器
                if not out.isOpened():
                    logger.info("mp4v 编码器初始化失败，尝试 MJPG 编码器...")
                    fourcc = cv2.VideoWriter_fourcc(*'MJPG')
                    out = cv2.VideoWriter(temp_video_path, fourcc, fps, frame_size)
        except Exception as e:
            logger.error(f"初始化视频编码器时出错: {e}")
            # 最后尝试使用默认编码器
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            out = cv2.VideoWriter(temp_video_path, fourcc, fps, frame_size)

        if not out.isOpened():
            logger.error(f"错误: 无法创建视频写入器或打开临时视频文件 {temp_video_path}")
            logger.error("请检查文件路径，权限，或尝试使用不同的fourcc编码器 (例如 'XVID')。")
            return None

        logger.info(f"开始生成临时视频帧并写入文件: {temp_video_path}")

        # 遍历每一页，根据音频时长渲染并写入帧
        for page_num in range(num_pages):
            page_duration_ms = page_durations_ms[page_num]
            page_duration_sec = page_duration_ms / 1000.0
            frames_for_this_page = max(1, int(page_duration_sec * fps)) # 确保至少有1帧

            logger.info(f"正在处理页面 {page_num + 1}/{num_pages} (时长: {page_duration_sec:.2f}秒, 帧数: {frames_for_this_page})...")

            # 加载并渲染当前页面
            page = doc.load_page(page_num)
            # 使用白色背景渲染PDF，确保没有透明度
            pix = page.get_pixmap(dpi=rendering_dpi, alpha=False)

            # 将 Pixmap 转换为 OpenCV 兼容的 NumPy 数组
            img_np = np.frombuffer(pix.samples, dtype=np.uint8).reshape((pix.h, pix.w, pix.n))

            # 根据通道数进行颜色空间转换 (PyMuPDF通常是RGB, OpenCV需要BGR)
            if pix.n == 3: # RGB
                frame = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
            elif pix.n == 4: # RGBA (不应该发生，因为我们设置了alpha=False)
                frame = cv2.cvtColor(img_np, cv2.COLOR_RGBA2BGR)
            else: # 灰度图
                # 转换为BGR格式，因为视频需要彩色帧
                frame = cv2.cvtColor(img_np, cv2.COLOR_GRAY2BGR)

            # 将当前页面的图片重复写入视频文件 frames_for_this_page 次
            for i in range(frames_for_this_page):
                out.write(frame)

            # 释放当前页面的 Pixmap 内存 (重要)
            del pix

        logger.info("所有页面视频帧生成完成。")

    except FileNotFoundError as e:
        logger.error(f"文件未找到错误: {e}")
        return None
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        return None

    finally:
        # 确保视频写入器被释放
        if 'out' in locals() and out is not None and out.isOpened():
            out.release()
            logger.info(f"临时视频文件已保存到: {os.path.abspath(temp_video_path)}")
        elif 'out' in locals() and out is not None:
            logger.warning("视频写入器未能成功打开，临时视频可能未生成。")

        # 确保PDF文档被关闭
        if doc is not None:
            doc.close()
            logger.info("PDF文件已关闭。")

        cv2.destroyAllWindows() # 关闭任何cv2可能创建的窗口

    # --- 使用 FFmpeg 合并视频和音频 ---
    logger.info("\n开始使用 FFmpeg 合并视频和音频...")
    if os.path.exists(temp_video_path) and os.path.exists(temp_audio_path):
        # 构建FFmpeg命令
        # 由于我们使用了XVID编码器，我们需要重新编码视频流
        # 使用H.264编码视频和AAC编码音频，这是MP4容器最常用的编码
        ffmpeg_command = [
            'ffmpeg',
            '-loglevel', 'error',  # 只显示错误信息，抑制进度输出
            '-hide_banner',        # 隐藏版本信息
            '-i', temp_video_path,
            '-i', temp_audio_path,
            '-c:v', 'libx264',  # 使用H.264编码视频
            '-preset', 'medium',  # 编码速度和质量的平衡
            '-crf', '23',  # 控制视频质量，值越小质量越高
            '-c:a', 'aac',  # 使用AAC编码音频
            '-b:a', '192k',  # 音频比特率
            '-strict', 'experimental',  # 对于某些旧版本的FFmpeg可能需要
            '-shortest',  # 确保输出视频长度与最短的输入流一致
            '-y',  # 自动覆盖输出文件如果已存在
            output_video_path
        ]

        logger.info(f"FFmpeg 命令: {' '.join(ffmpeg_command)}")

        try:
            # 执行FFmpeg命令
            process = subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True)
            logger.info("FFmpeg 合并成功！")
            logger.debug("FFmpeg stdout:\n" + process.stdout)
            logger.debug("FFmpeg stderr:\n" + process.stderr) # FFmpeg 进度信息通常在 stderr

            logger.info(f"\n最终视频文件已保存到: {os.path.abspath(output_video_path)}")

        except subprocess.CalledProcessError as e:
            logger.error(f"错误: FFmpeg 合并失败！")
            logger.error(f"Command: {' '.join(e.cmd)}")
            logger.error(f"Return Code: {e.returncode}")
            logger.error(f"Stdout: {e.stdout}")
            logger.error(f"Stderr: {e.stderr}") # FFmpeg 错误信息通常在 stderr
            return None
        except Exception as e:
            logger.error(f"执行 FFmpeg 命令时发生未知错误: {e}")
            return None

    else:
        logger.error("\n错误: 临时视频文件或音频文件未生成，跳过合并步骤。")
        return None

    # --- 清理临时文件 ---
    if clean_temp_files:
        logger.info("\n正在清理临时文件...")
        if os.path.exists(temp_video_path):
            try:
                os.remove(temp_video_path)
                logger.info(f"已删除临时视频文件: {temp_video_path}")
            except OSError as e:
                logger.warning(f"警告: 无法删除临时视频文件 {temp_video_path}: {e}")

        if os.path.exists(temp_audio_path):
            try:
                os.remove(temp_audio_path)
                logger.info(f"已删除临时音频文件: {temp_audio_path}")
            except OSError as e:
                logger.warning(f"警告: 无法删除临时音频文件 {temp_audio_path}: {e}")

    logger.info("\n视频生成完毕。")
    return output_video_path