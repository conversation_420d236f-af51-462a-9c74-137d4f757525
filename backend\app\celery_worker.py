import os
import sys
import logging
from celery import Celery
from celery.signals import task_success, task_failure, task_revoked

# 动态添加当前目录到 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,  # 改为INFO级别，避免过多调试信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("celery.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 抑制第三方库的调试日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("requests").setLevel(logging.WARNING)
logging.getLogger("supabase").setLevel(logging.WARNING)
logging.getLogger("storage3").setLevel(logging.WARNING)
logging.getLogger("hpack").setLevel(logging.WARNING)
logging.getLogger("h2").setLevel(logging.WARNING)
# 抑制pydub和FFmpeg的调试日志
logging.getLogger("pydub").setLevel(logging.ERROR)
logging.getLogger("pydub.converter").setLevel(logging.ERROR)
logging.getLogger("pydub.utils").setLevel(logging.ERROR)
logging.getLogger("pydub.silence").setLevel(logging.ERROR)
logging.getLogger("pydub.playback").setLevel(logging.ERROR)

logger = logging.getLogger(__name__)

# 从环境变量读取 Redis 配置，便于本地和生产灵活切换
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
logger.info(f"使用Redis URL: {REDIS_URL}")

# 从环境变量读取重试配置，以节省MiniMax API费用
MAX_RETRIES = int(os.getenv("CELERY_MAX_RETRIES", "1"))  # 默认最多重试1次
RETRY_DELAY = int(os.getenv("CELERY_RETRY_DELAY", "10"))  # 默认重试延迟10秒
logger.info(f"Celery重试配置: 最大重试次数={MAX_RETRIES}, 重试延迟={RETRY_DELAY}秒")
logger.info(f"环境变量CELERY_MAX_RETRIES: {os.getenv('CELERY_MAX_RETRIES', 'NOT_SET')}")

celery_app = Celery(
    "app_tasks",
    broker=REDIS_URL,
    backend=REDIS_URL
)

# Celery配置
celery_app.conf.update(
    # 任务结果过期时间（1天）
    result_expires=86400,
    # 启用任务事件，用于监控
    worker_send_task_events=True,
    # 启用结果后端
    task_track_started=True,
    # 任务默认超时时间（30分钟）
    task_time_limit=1800,
    # 任务软超时时间（25分钟）
    task_soft_time_limit=1500,
    # 任务默认重试次数（从环境变量读取，以节省MiniMax API费用）
    task_default_retries=MAX_RETRIES,
    # 任务默认重试延迟（从环境变量读取）
    task_default_retry_delay=RETRY_DELAY,
    # 禁用预取，确保任务均匀分配
    worker_prefetch_multiplier=1,
    # 并发工作进程数（根据CPU核心数自动设置）
    worker_concurrency=os.cpu_count() or 4,
)

# 导入任务处理模块
from services.task_processor import process_task_sync, get_voice_settings
from services.supabase_storage import upload_file_to_supabase

# 主要任务：处理PDF和脚本生成视频
@celery_app.task(bind=True, name="process_voiceover_task")
def process_task_celery(self, task_id, pdf_path, script_path, voice_option, user_id=None):
    """
    处理视频生成任务的Celery任务

    参数:
        task_id: 任务ID
        pdf_path: PDF文件路径
        script_path: 脚本文件路径
        voice_option: 语音选项
        user_id: 用户ID（可选）

    返回:
        包含任务结果的字典
    """
    try:
        logger.info(f"开始处理任务 {task_id}")

        # 更新任务状态为处理中
        self.update_state(state="PROCESSING", meta={
            "status": "processing",
            "progress": 10,
            "message": "开始处理任务..."
        })

        # 获取语音设置
        voice_settings = get_voice_settings(voice_option)

        # 调用同步版本的处理函数
        result = process_task_sync(
            task_id=task_id,
            pdf_path=pdf_path,
            script_path=script_path,
            voice_settings=voice_settings
        )

        # 处理结果
        if result["success"]:
            logger.info(f"任务 {task_id} 处理成功")
            return {
                "status": "completed",
                "result_url": result["result_url"],
                "supabase_url": result.get("supabase_url"),
                "message": "任务处理成功"
            }
        else:
            logger.error(f"任务 {task_id} 处理失败: {result['error']}")
            raise Exception(result["error"])

    except Exception as e:
        logger.error(f"任务处理异常: {str(e)}", exc_info=True)
        # 重试任务（根据环境变量配置，以节省MiniMax API费用）
        if self.request.retries < MAX_RETRIES:
            logger.info(f"尝试重试任务 {task_id}，当前重试次数: {self.request.retries + 1}/{MAX_RETRIES}")
            logger.warning("注意：重试会消耗额外的MiniMax API费用")
            raise self.retry(exc=e, countdown=RETRY_DELAY)
        else:
            logger.error(f"任务 {task_id} 已达到最大重试次数({MAX_RETRIES})，停止重试以节省API费用")
        return {
            "status": "failed",
            "error_message": str(e)
        }

# 任务成功信号处理
@task_success.connect
def task_success_handler(sender=None, **kwargs):
    """任务成功完成时的处理函数"""
    task_id = sender.request.id
    result = kwargs.get('result')
    logger.info(f"任务 {task_id} 成功完成: {result}")

# 任务失败信号处理
@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, **kwargs):
    """任务失败时的处理函数"""
    logger.error(f"任务 {task_id} 失败: {exception}")

# 任务被撤销信号处理
@task_revoked.connect
def task_revoked_handler(sender=None, request=None, **kwargs):
    """任务被撤销时的处理函数"""
    task_id = request.id if request else "未知"
    logger.warning(f"任务 {task_id} 被撤销")