# fly.toml app configuration file for FastAPI application
app = "aipresenter-api"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.api"

[env]
  PORT = "8000"
  # Redis配置
  REDIS_URL = "redis://default:<EMAIL>:6379"

  # 其他环境变量将从.env文件读取或通过 flyctl secrets set 设置

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

#[mounts]
#  source = "aipresenter_data"
#  destination = "/app/data"
