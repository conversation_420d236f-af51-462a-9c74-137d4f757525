"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"

export function ThemeProvider({ children, ...props }) {
  const [mounted, setMounted] = React.useState(false)

  // Ensure theme transitions only happen after hydration
  React.useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <NextThemesProvider {...props}>
      <div className={mounted ? "animate-theme-transition" : "opacity-0"}>{children}</div>
    </NextThemesProvider>
  )
}
