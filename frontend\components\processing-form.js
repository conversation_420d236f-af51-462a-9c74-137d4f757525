// "use client"

// import { useState, useEffect } from "react"
// import axios from "axios"
// import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
// import { Label } from "@/components/ui/label"
// import { Progress } from "@/components/ui/progress"
// import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
// import { FileUp, FileText, Volume2, AlertCircle, Download, CheckCircle2, Upload } from "lucide-react"
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
// import { cn } from "@/lib/utils"
// import { Animate } from "@/components/animations/animate"
// import { EnhancedButton } from "@/components/ui/enhanced-button"
// import { motion, AnimatePresence } from "framer-motion"

// export function ProcessingForm() {
//   // State management
//   const [pdfFile, setPdfFile] = useState(null)
//   const [txtFile, setTxtFile] = useState(null)
//   const [scriptText, setScriptText] = useState("")
//   const [scriptInputMethod, setScriptInputMethod] = useState("file") // "file" or "text"
//   const [characterCount, setCharacterCount] = useState(0)
//   const [selectedVoice, setSelectedVoice] = useState("male-qn-qingse")
//   const [uploading, setUploading] = useState(false)
//   const [taskId, setTaskId] = useState(null)
//   const [taskStatus, setTaskStatus] = useState("idle")
//   const [taskResultUrl, setTaskResultUrl] = useState(null)
//   const [error, setError] = useState(null)
//   const [progress, setProgress] = useState(0)
//   const [dragActive, setDragActive] = useState({ pdf: false, txt: false })

//   // Handle file selection
//   const handlePdfChange = (e) => {
//     if (e.target.files && e.target.files[0]) {
//       setPdfFile(e.target.files[0])
//     }
//   }

//   const handleTxtChange = (e) => {
//     if (e.target.files && e.target.files[0]) {
//       setTxtFile(e.target.files[0])
//     }
//   }

//   // Handle drag and drop
//   const handleDrag = (e, type, active) => {
//     e.preventDefault()
//     e.stopPropagation()
//     setDragActive((prev) => ({ ...prev, [type]: active }))
//   }

//   const handleDrop = (e, type) => {
//     e.preventDefault()
//     e.stopPropagation()
//     setDragActive((prev) => ({ ...prev, [type]: false }))

//     if (e.dataTransfer.files && e.dataTransfer.files[0]) {
//       if (type === "pdf") {
//         setPdfFile(e.dataTransfer.files[0])
//       } else if (type === "txt") {
//         setTxtFile(e.dataTransfer.files[0])
//       }
//     }
//   }

//   // Handle script text input
//   const handleScriptTextChange = (e) => {
//     const text = e.target.value;
//     setScriptText(text);
//     setCharacterCount(text.length);

//     // If text is entered, create a virtual text file
//     if (text.trim()) {
//       const blob = new Blob([text], { type: 'text/plain' });
//       const file = new File([blob], "script.txt", { type: 'text/plain' });
//       setTxtFile(file);
//     } else {
//       setTxtFile(null);
//     }
//   }

//   // Handle script input method change
//   const handleScriptInputMethodChange = (method) => {
//     setScriptInputMethod(method);

//     // Reset the other input method
//     if (method === "file") {
//       setScriptText("");
//       setCharacterCount(0);
//     } else {
//       setTxtFile(null);
//     }
//   }

//   // Handle voice selection
//   const handleVoiceChange = (value) => {
//     setSelectedVoice(value)
//   }

//   // Handle form submission
//   const handleSubmit = async (e) => {
//     e.preventDefault()

//     // Client-side validation
//     if (!pdfFile) {
//       setError("Please select a PDF file")
//       return
//     }

//     if (!txtFile && scriptInputMethod === "file") {
//       setError("Please select a script file")
//       return
//     }

//     if (!scriptText.trim() && scriptInputMethod === "text") {
//       setError("Please enter script text")
//       return
//     }

//     // Character count validation
//     if (scriptInputMethod === "text" && characterCount > 5000) {
//       setError("Script text exceeds the 5000 character limit")
//       return
//     }

//     // Reset state
//     setUploading(true)
//     setError(null)
//     setTaskId(null)
//     setTaskStatus("idle")
//     setTaskResultUrl(null)
//     setProgress(0)

//     // Prepare form data
//     const formData = new FormData()
//     formData.append("pdf_file", pdfFile)
//     if (txtFile) {
//       formData.append("txt_file", txtFile)
//     }
//     formData.append("voice_option", selectedVoice)

//     try {
//       // Send request to backend API
//       const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || "http://localhost:8000"
//       console.log("Sending request to:", `${BACKEND_API_BASE_URL}/api/submit-job`)

//       const response = await axios.post(`${BACKEND_API_BASE_URL}/api/submit-job`, formData, {
//         headers: {
//           "Content-Type": "multipart/form-data",
//         },
//       })

//       // Handle successful response
//       console.log("Task submitted successfully:", response.data)
//       setTaskId(response.data.task_id)
//       setTaskStatus("queued")
//       setProgress(25)
//     } catch (err) {
//       // Handle error
//       console.error("Failed to submit task:", err)
//       setError(err.response?.data?.detail || "Failed to submit task, please try again")
//       setTaskStatus("failed")
//     } finally {
//       setUploading(false)
//     }
//   }

//   // Poll task status
//   useEffect(() => {
//     let intervalId = null

//     if (taskId && taskStatus !== "completed" && taskStatus !== "failed") {
//       // Set polling interval
//       intervalId = setInterval(async () => {
//         try {
//           const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || "http://localhost:8000"
//           const response = await axios.get(`${BACKEND_API_BASE_URL}/api/status/${taskId}`)

//           // Update task status
//           setTaskStatus(response.data.status)

//           // Update progress
//           if (response.data.status === "queued") setProgress(25)
//           if (response.data.status === "processing") setProgress(60)
//           if (response.data.status === "completed") setProgress(100)

//           // Handle completed status
//           if (response.data.status === "completed") {
//             if (intervalId) clearInterval(intervalId)
//             setTaskResultUrl(response.data.result_url)
//           }

//           // Handle failed status
//           if (response.data.status === "failed") {
//             if (intervalId) clearInterval(intervalId)
//             setError(response.data.error_message || "Processing failed, please try again")
//             setTaskStatus("failed")
//           }
//         } catch (err) {
//           // Handle polling error
//           console.error("Failed to get task status:", err)
//           if (intervalId) clearInterval(intervalId)
//           setError("Failed to get task status, please refresh the page")
//           setTaskStatus("failed")
//         }
//       }, 3000) // Poll every 3 seconds
//     }

//     // Cleanup function
//     return () => {
//       if (intervalId) {
//         clearInterval(intervalId)
//       }
//     }
//   }, [taskId, taskStatus])

//   // Handle result download
//   const handleDownload = () => {
//     if (taskResultUrl) {
//       const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || "http://localhost:8000"

//       // 构建下载URL - 只需在原始URL后添加/download
//       const downloadUrl = `${taskResultUrl}/download`
//       const fullUrl = `${BACKEND_API_BASE_URL}${downloadUrl}`

//       // 直接导航到下载URL，浏览器会自动下载文件
//       window.location.href = fullUrl
//     }
//   }

//   // Render status indicator
//   const renderStatusIndicator = () => {
//     if (!taskId) return null

//     let statusText = ""
//     let statusIcon = null

//     switch (taskStatus) {
//       case "queued":
//         statusText = "Task queued, waiting for processing..."
//         break
//       case "processing":
//         statusText = "Processing your files..."
//         break
//       case "completed":
//         statusText = "Processing complete!"
//         statusIcon = <CheckCircle2 className="h-5 w-5 text-green-500" />
//         break
//       case "failed":
//         statusText = `Processing failed: ${error || "Unknown error"}`
//         statusIcon = <AlertCircle className="h-5 w-5 text-red-500" />
//         break
//       default:
//         statusText = "Waiting for submission..."
//     }

//     return (
//       <div className="mt-6 space-y-4">
//         <div className="flex items-center gap-2">
//           {statusIcon}
//           <p className="text-sm font-medium">{statusText}</p>
//         </div>
//         <Progress
//           value={progress}
//           className="h-2 bg-white/10"
//           indicatorClassName={cn(
//             "transition-all duration-300",
//             taskStatus === "completed" ? "bg-theme-blue" : "bg-white",
//           )}
//         />
//       </div>
//     )
//   }

//   // Render result section
//   const renderResult = () => {
//     if (taskStatus === "completed" && taskResultUrl) {
//       const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || "http://localhost:8000"
//       // 使用原始results路径用于预览
//       const fullUrl = `${BACKEND_API_BASE_URL}${taskResultUrl}`
//       const isVideo = taskResultUrl.toLowerCase().endsWith(".mp4")

//       return (
//         <Animate type="fade">
//           <Card className="mt-8 border-white/10 subtle-glass">
//             <CardHeader className="pb-2">
//               <div className="flex items-center gap-2">
//                 <motion.div
//                   initial={{ scale: 0 }}
//                   animate={{ scale: 1 }}
//                   transition={{ type: "spring", stiffness: 400, damping: 10 }}
//                 >
//                   <CheckCircle2 className="h-5 w-5 text-theme-blue" />
//                 </motion.div>
//                 <CardTitle className="text-white">Processing Complete</CardTitle>
//               </div>
//               <CardDescription>Your {isVideo ? "video" : "audio"} is ready</CardDescription>
//             </CardHeader>
//             <CardContent>
//               <motion.div
//                 className="rounded-lg overflow-hidden border border-white/10 bg-black/20"
//                 initial={{ opacity: 0 }}
//                 animate={{ opacity: 1 }}
//                 transition={{ delay: 0.2 }}
//               >
//                 {isVideo ? (
//                   <video controls src={fullUrl} className="w-full h-auto"></video>
//                 ) : (
//                   <div className="p-4 flex justify-center">
//                     <audio controls src={fullUrl} className="w-full max-w-md"></audio>
//                   </div>
//                 )}
//               </motion.div>
//             </CardContent>
//             <CardFooter>
//               <EnhancedButton
//                 onClick={handleDownload}
//                 className="w-full gap-2 bg-white text-black hover:bg-white/90"
//                 color="blue"
//               >
//                 <Download className="h-4 w-4" />
//                 Download {isVideo ? "Video" : "Audio"} File
//               </EnhancedButton>
//             </CardFooter>
//           </Card>
//         </Animate>
//       )
//     }
//     return null
//   }

//   // Render error message
//   const renderError = () => {
//     if (error && !taskId) {
//       return (
//         <Alert variant="destructive" className="mt-4 border-red-900/50 bg-red-950/20">
//           <AlertCircle className="h-4 w-4" />
//           <AlertTitle>Error</AlertTitle>
//           <AlertDescription>{error}</AlertDescription>
//         </Alert>
//       )
//     }
//     return null
//   }

//   // File upload area
//   const FileUploadArea = ({ type, file, onChange, onDragEnter, onDragLeave, onDragOver, onDrop, isDragActive }) => (
//     <motion.div
//       className={cn(
//         "border-2 border-dashed rounded-lg p-6 transition-all text-center cursor-pointer",
//         isDragActive
//           ? "border-theme-blue bg-theme-blue/5"
//           : file
//             ? "border-white/50 bg-white/5"
//             : "border-muted-foreground/25 hover:border-white/30",
//         "subtle-hover",
//       )}
//       onDragEnter={onDragEnter}
//       onDragLeave={onDragLeave}
//       onDragOver={onDragOver}
//       onDrop={onDrop}
//       onClick={() => document.getElementById(`${type}File`).click()}
//       whileHover={{ scale: 1.01 }}
//       transition={{ type: "spring", stiffness: 400, damping: 17 }}
//     >
//       <input
//         type="file"
//         id={`${type}File`}
//         accept={type === "pdf" ? ".pdf" : ".txt"}
//         onChange={onChange}
//         disabled={uploading || taskId !== null}
//         className="hidden"
//       />

//       <div className="flex flex-col items-center gap-2">
//         {file ? (
//           <>
//             <motion.div
//               initial={{ scale: 0 }}
//               animate={{ scale: 1 }}
//               transition={{ type: "spring", stiffness: 400, damping: 10 }}
//             >
//               <CheckCircle2 className="h-10 w-10 text-theme-blue" />
//             </motion.div>
//             <div>
//               <p className="font-medium dark:text-white text-black">{file.name}</p>
//               <p className="text-sm text-muted-foreground">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
//             </div>
//           </>
//         ) : (
//           <>
//             <motion.div
//               className="p-3 rounded-full bg-white/10 mb-2"
//               animate={{
//                 boxShadow: isDragActive
//                   ? [
//                       "0 0 0 0 rgba(33, 150, 243, 0)",
//                       "0 0 10px 2px rgba(33, 150, 243, 0.3)",
//                       "0 0 0 0 rgba(33, 150, 243, 0)",
//                     ]
//                   : "none",
//               }}
//               transition={{ duration: 2, repeat: isDragActive ? Number.POSITIVE_INFINITY : 0 }}
//             >
//               {type === "pdf" ? <FileUp className="h-6 w-6 dark:text-white text-black" /> : <FileText className="h-6 w-6 dark:text-white text-black" />}
//             </motion.div>
//             <p className="font-medium dark:text-white text-black">
//               {type === "pdf" ? "Upload PPT File (PDF format)" : "Upload Script File (TXT format)"}
//             </p>
//             <p className="text-sm text-muted-foreground">Drag and drop file here or click to upload</p>
//           </>
//         )}
//       </div>
//     </motion.div>
//   )

//   return (
//     <Animate type="fade">
//       <Card className="w-full border-white/10 subtle-glass" id="processing-form">
//         <CardHeader className="space-y-1">
//           <CardTitle className="text-2xl gradient-text">AI Voiceover Service</CardTitle>
//           <CardDescription>
//             Upload your PPT file and script, select a suitable voice, and we'll generate a professional AI voiceover
//           </CardDescription>
//         </CardHeader>

//         <CardContent>
//           <Tabs defaultValue="upload" className="w-full">
//             <TabsList className="grid grid-cols-2 mb-8 bg-black/20 border border-white/10">
//               <TabsTrigger
//                 value="upload"
//                 className="data-[state=active]:bg-theme-blue data-[state=active]:text-white transition-all duration-300"
//               >
//                 Upload Files
//               </TabsTrigger>
//               <TabsTrigger
//                 value="voice"
//                 className="data-[state=active]:bg-theme-blue data-[state=active]:text-white transition-all duration-300"
//               >
//                 Select Voice
//               </TabsTrigger>
//             </TabsList>

//             <AnimatePresence mode="wait">
//               <TabsContent value="upload" className="space-y-6">
//                 <motion.div
//                   className="grid grid-cols-1 md:grid-cols-2 gap-6"
//                   initial={{ opacity: 0 }}
//                   animate={{ opacity: 1 }}
//                   exit={{ opacity: 0 }}
//                   transition={{ duration: 0.2 }}
//                 >
//                   <div className="space-y-2">
//                     <Label htmlFor="pdfFile" className="flex items-center gap-2 dark:text-white text-black">
//                       <FileUp className="h-4 w-4" />
//                       PPT File (PDF format) *
//                     </Label>
//                     <FileUploadArea
//                       type="pdf"
//                       file={pdfFile}
//                       onChange={handlePdfChange}
//                       onDragEnter={(e) => handleDrag(e, "pdf", true)}
//                       onDragLeave={(e) => handleDrag(e, "pdf", false)}
//                       onDragOver={(e) => handleDrag(e, "pdf", true)}
//                       onDrop={(e) => handleDrop(e, "pdf")}
//                       isDragActive={dragActive.pdf}
//                     />
//                   </div>

//                   <div className="space-y-2">
//                     <Label htmlFor="scriptInput" className="flex items-center gap-2 dark:text-white text-black">
//                       <FileText className="h-4 w-4" />
//                       Script Input *
//                     </Label>

//                     {/* Script Input Method Tabs */}
//                     <div className="mb-4">
//                       <div className="flex border-b border-border">
//                         <button
//                           type="button"
//                           onClick={() => handleScriptInputMethodChange("file")}
//                           className={`pb-2 px-4 text-sm font-medium ${
//                             scriptInputMethod === "file"
//                               ? "border-b-2 border-theme-blue text-theme-blue"
//                               : "text-muted-foreground hover:text-foreground"
//                           }`}
//                         >
//                           Upload File
//                         </button>
//                         <button
//                           type="button"
//                           onClick={() => handleScriptInputMethodChange("text")}
//                           className={`pb-2 px-4 text-sm font-medium ${
//                             scriptInputMethod === "text"
//                               ? "border-b-2 border-theme-blue text-theme-blue"
//                               : "text-muted-foreground hover:text-foreground"
//                           }`}
//                         >
//                           Enter Text
//                         </button>
//                       </div>
//                     </div>

//                     {/* File Upload Area */}
//                     {scriptInputMethod === "file" && (
//                       <FileUploadArea
//                         type="txt"
//                         file={txtFile}
//                         onChange={handleTxtChange}
//                         onDragEnter={(e) => handleDrag(e, "txt", true)}
//                         onDragLeave={(e) => handleDrag(e, "txt", false)}
//                         onDragOver={(e) => handleDrag(e, "txt", true)}
//                         onDrop={(e) => handleDrop(e, "txt")}
//                         isDragActive={dragActive.txt}
//                       />
//                     )}

//                     {/* Text Input Area */}
//                     {scriptInputMethod === "text" && (
//                       <div className="space-y-2">
//                         <textarea
//                           id="scriptText"
//                           value={scriptText}
//                           onChange={handleScriptTextChange}
//                           disabled={uploading || taskId !== null}
//                           placeholder={"Enter your script text here.\n" +
//                             "Use <Slide N> tags to mark slide transitions paragraph:\n\n" +
//                             "<Slide 1>\n" +
//                             "some text\n" +
//                             "<Slide 2>\n" +
//                             "some text"}
//                           className="w-full h-40 px-3 py-2 bg-background border border-input rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary resize-none dark:text-white text-black"
//                         />
//                         <div className="flex justify-between text-xs text-muted-foreground">
//                           <span className={characterCount > 5000 ? "text-red-500" : ""}>
//                             {characterCount} / 5000 characters
//                           </span>
//                         </div>
//                       </div>
//                     )}
//                   </div>
//                 </motion.div>
//               </TabsContent>

//               <TabsContent value="voice" className="space-y-6">
//                 <motion.div
//                   className="space-y-4"
//                   initial={{ opacity: 0 }}
//                   animate={{ opacity: 1 }}
//                   exit={{ opacity: 0 }}
//                   transition={{ duration: 0.2 }}
//                 >
//                   <Label htmlFor="voiceOption" className="flex items-center gap-2 dark:text-white text-black">
//                     <Volume2 className="h-4 w-4" />
//                     Select Voiceover Voice
//                   </Label>

//                   <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
//                     <VoiceOption
//                       id="male-qn-qingse"
//                       name="Young Male"
//                       description="Youthful and energetic, suitable for educational content"
//                       selected={selectedVoice === "male-qn-qingse"}
//                       onClick={() => handleVoiceChange("male-qn-qingse")}
//                       disabled={uploading || taskId !== null}
//                     />
//                     <VoiceOption
//                       id="male-qn-jingying"
//                       name="Professional Male"
//                       description="Mature and authoritative, ideal for business presentations"
//                       selected={selectedVoice === "male-qn-jingying"}
//                       onClick={() => handleVoiceChange("male-qn-jingying")}
//                       disabled={uploading || taskId !== null}
//                     />
//                     <VoiceOption
//                       id="female-shaonv"
//                       name="Young Female"
//                       description="Fresh and sweet, suitable for lifestyle content"
//                       selected={selectedVoice === "female-shaonv"}
//                       onClick={() => handleVoiceChange("female-shaonv")}
//                       disabled={uploading || taskId !== null}
//                     />
//                     <VoiceOption
//                       id="female-yujie"
//                       name="Professional Female"
//                       description="Elegant and sophisticated, perfect for formal presentations"
//                       selected={selectedVoice === "female-yujie"}
//                       onClick={() => handleVoiceChange("female-yujie")}
//                       disabled={uploading || taskId !== null}
//                     />
//                   </div>
//                 </motion.div>
//               </TabsContent>
//             </AnimatePresence>
//           </Tabs>

//           {renderStatusIndicator()}
//           {renderError()}
//         </CardContent>

//         <CardFooter className="flex flex-col">
//           <EnhancedButton
//             type="submit"
//             className="w-full gap-2 bg-white text-black hover:bg-white/90"
//             size="lg"
//             disabled={uploading || taskId !== null || !pdfFile || !txtFile}
//             onClick={handleSubmit}
//             loading={uploading}
//             color="blue"
//           >
//             <Upload className="h-4 w-4" />
//             Start Processing
//           </EnhancedButton>
//         </CardFooter>

//         {renderResult()}
//       </Card>
//     </Animate>
//   )
// }

// function VoiceOption({ id, name, description, selected, onClick, disabled }) {
//   return (
//     <motion.div
//       className={cn(
//         "border rounded-lg p-4 cursor-pointer transition-all",
//         selected ? "border-theme-blue bg-theme-blue/10" : "hover:border-white/30 border-white/10 bg-black/20",
//         disabled && "opacity-50 cursor-not-allowed",
//         "subtle-hover",
//       )}
//       onClick={disabled ? undefined : onClick}
//       whileHover={disabled ? {} : { scale: 1.02 }}
//       transition={{ type: "spring", stiffness: 400, damping: 17 }}
//     >
//       <div className="flex items-start gap-3">
//         <div
//           className={cn(
//             "mt-1 h-4 w-4 rounded-full border flex items-center justify-center",
//             selected ? "border-theme-blue" : "border-muted-foreground",
//           )}
//         >
//           {selected && (
//             <motion.div
//               className="h-2 w-2 rounded-full bg-theme-blue"
//               initial={{ scale: 0 }}
//               animate={{ scale: 1 }}
//               transition={{ type: "spring", stiffness: 400, damping: 10 }}
//             />
//           )}
//         </div>
//         <div>
//           <p className="font-medium dark:text-white text-black">{name}</p>
//           <p className="text-sm text-muted-foreground">{description}</p>
//         </div>
//       </div>
//     </motion.div>
//   )
// }
