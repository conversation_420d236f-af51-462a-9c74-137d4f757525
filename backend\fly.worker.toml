# fly.toml app configuration file for Celery Worker
app = "aipresenter-worker"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.worker"

[env]
  # Redis配置
  REDIS_URL = "redis://default:<EMAIL>:6379"

  # Celery重试配置（以节省MiniMax API费用）
  CELERY_MAX_RETRIES = "0"  # 设置为0表示不重试，节省API费用
  CELERY_RETRY_DELAY = "10"  # 如果需要重试，延迟10秒

  # 其他环境变量将从.env文件读取或通过 flyctl secrets set 设置

# Worker进程配置
[processes]
  app = "bash -c 'cd /app && python -m app.worker_health & python -m celery -A app.celery_worker worker --loglevel=debug --concurrency=2'"

# 添加HTTP服务配置，用于健康检查
[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[vm]]
  memory = "2gb"
  cpu_kind = "shared"
  cpus = 2

#[mounts]
 # source = "aipresenter_data"
 # destination = "/app/data"
