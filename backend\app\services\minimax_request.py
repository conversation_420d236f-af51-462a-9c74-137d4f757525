import json
import time
from typing import Iterator
import requests
import os
from dotenv import load_dotenv
import logging
import subprocess


logging.basicConfig(level=logging.INFO,
                    format='%(levelname)s - %(filename)s:%(lineno)d - %(message)s')
logger = logging.getLogger(__name__)


# 优先直接从系统环境变量获取 针对线上部署环境，没有.env文件的问题
group_id = os.environ.get('GROUP_ID')
api_key = os.environ.get('MINIMAX_APIKEY')

if group_id is None or api_key is None:
    logger.info("Some environment variables are missing, trying to load from .env file...")
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取父目录（app/）
    parent_dir = os.path.dirname(current_dir)
    # 获取根目录（backend/）
    root_dir = os.path.dirname(parent_dir)

    # 尝试多个可能的 .env 文件位置
    possible_env_paths = [
        os.path.join(current_dir, '.env'),  # services/.env
        os.path.join(parent_dir, '.env'),   # app/.env
        os.path.join(root_dir, '.env'),     # backend/.env
    ]

    # 尝试加载第一个存在的 .env 文件
    for env_path in possible_env_paths:
        if os.path.isfile(env_path):
            load_dotenv(env_path)
            logger.info(f"Loaded .env from {env_path}")
            break

    # 再次尝试获取环境变量
    group_id = os.environ.get('GROUP_ID')
    api_key = os.environ.get('MINIMAX_APIKEY')

# 检查是否成功获取所有必要的环境变量
missing_vars = []
if group_id is None:
    missing_vars.append('GROUP_ID')
if api_key is None:
    missing_vars.append('MINIMAX_APIKEY')

if missing_vars:
    error_msg = f"The following environment variables are not set: {', '.join(missing_vars)}"
    logger.error(error_msg)
    raise ValueError(error_msg)

# 记录成功获取环境变量
logger.info("#####Successfully loaded all required environment variables: minimax#####")

file_format = 'mp3'  # 支持 mp3/pcm/flac



def build_tts_headers() -> dict:
    """构建非流式TTS请求头"""
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    return headers

def build_tts_body(text: str, custom_voice_setting: dict = None) -> dict:
    """构建非流式TTS请求体"""
    default_voice_setting = {
        "voice_id":"male-qn-qingse",
        "speed":1.0,
        "vol":1.0,
        "pitch":0
    }

    voice_setting = default_voice_setting.copy()
    if custom_voice_setting is not None:
        voice_setting.update(custom_voice_setting)

    body = {
        "model":"speech-02-hd",

        "text":text,
        "stream":False,
        "voice_setting":voice_setting,
        "audio_setting":{
            "sample_rate":44100,  # 标准CD质量，更好的兼容性
            "bitrate":128000,     # 128k比特率（API支持的最高值）
            "format":"mp3",       # MP3格式
            "channel":1           # 单声道，适合语音
        },
        "language_boost":"Chinese"
    }
    return body



def call_tts_non_stream(text: str, custom_voice_setting: dict = None) -> bytes:
    """非流式TTS请求 - 基于MiniMax官方示例"""

    url = "https://api.minimax.chat/v1/t2a_v2?GroupId=" + group_id
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    tts_body = build_tts_body(text, custom_voice_setting)

    # 记录请求参数（用于诊断）
    logger.info(f"MiniMax请求参数: model={tts_body['model']}, format={tts_body['audio_setting']['format']}, "
                f"sample_rate={tts_body['audio_setting']['sample_rate']}, "
                f"bitrate={tts_body['audio_setting']['bitrate']}")

    try:
        # 发送非流式请求
        response = requests.request(
            "POST",
            url,
            stream=True,  #官方非流式的示例此处为True，应该不是指流式的意思
            headers=headers,
            data=json.dumps(tts_body),
        )

        logger.info(f"MiniMax响应状态码: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"MiniMax API请求失败: {response.status_code} - {response.text}")
            return b""

        # 解析响应
        try:
            parsed_json = json.loads(response.text)

            # 检查错误状态
            if "base_resp" in parsed_json:
                base_resp = parsed_json["base_resp"]
                if base_resp.get("status_code", 0) != 0:
                    logger.error(f"MiniMax API错误: {base_resp}")
                    return b""

            # 获取音频数据
            if "data" in parsed_json and parsed_json["data"] is not None:
                if "audio" in parsed_json["data"]:
                    audio_hex = parsed_json["data"]["audio"]
                    if audio_hex:
                        # 将hex字符串转换为字节
                        audio_bytes = bytes.fromhex(audio_hex)
                        logger.info(f"成功获取音频数据: hex长度={len(audio_hex)}, 字节长度={len(audio_bytes)}")
                        return audio_bytes
                    else:
                        logger.error("MiniMax返回的音频数据为空")
                        return b""
                else:
                    logger.error("MiniMax响应中没有audio字段")
                    return b""
            else:
                logger.error("MiniMax响应中没有data字段或data为空")
                return b""

        except json.JSONDecodeError as e:
            logger.error(f"MiniMax响应JSON解析失败: {e}")
            # logger.error(f"响应内容: {response.text[:500]}...")
            return b""
        except ValueError as e:
            logger.error(f"音频hex解码失败: {e}")
            return b""

    except requests.exceptions.Timeout:
        logger.error("MiniMax API请求超时")
        return b""
    except requests.exceptions.RequestException as e:
        logger.error(f"MiniMax API请求异常: {e}")
        return b""
    except Exception as e:
        logger.error(f"MiniMax非流式处理异常: {e}")
        return b""


def audio_play(audio_stream: Iterator[bytes]) -> bytes:
    audio = b""
    chunk_count = 0
    total_hex_length = 0

    logger.info("开始组装音频数据...")

    for chunk in audio_stream:
        if chunk is not None and chunk != '\n':
            try:
                chunk_count += 1
                total_hex_length += len(chunk)
                decoded_hex = bytes.fromhex(chunk)
                audio += decoded_hex
                logger.debug(f"处理音频chunk {chunk_count}: hex长度={len(chunk)}, 解码后长度={len(decoded_hex)}")
            except ValueError as e:
                logger.error(f"音频chunk {chunk_count} hex解码失败: {e}, chunk内容: {chunk[:100]}...")
                continue
            except Exception as e:
                logger.error(f"处理音频chunk {chunk_count} 时发生错误: {e}")
                continue

    logger.info(f"音频数据组装完成: 处理了{chunk_count}个chunk, 总hex长度={total_hex_length}, 最终音频大小={len(audio)}字节")

    if len(audio) == 0:
        logger.error("警告: 没有获取到任何音频数据!")

    return audio


def minimax_request(text, save_folder, custom_voice_setting:dict=None, output_filename=None):
    """
    调用MiniMax API生成语音

    参数:
    text (str): 要转换为语音的文本
    save_folder (str): 保存结果的文件夹路径
    custom_voice_setting (dict, optional): 自定义语音设置
    output_filename (str, optional): 指定输出文件名，不包含路径和扩展名

    返回:
    str: 生成的音频文件路径
    """
    # 使用非流式请求
    audio = call_tts_non_stream(text, custom_voice_setting)

    # 检查音频数据是否为空
    if not audio or len(audio) == 0:
        logger.error("MiniMax API返回的音频数据为空")
        raise Exception("Failed to generate audio: empty response from MiniMax API")

    # 确保保存文件夹存在
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)

    # 生成文件名
    if output_filename:
        file_name = f'{save_folder}/{output_filename}.{file_format}'
    else:
        timestamp = int(time.time())
        file_name = f'{save_folder}/output_total_{timestamp}.{file_format}'

    # 保存原始音频文件
    raw_file_name = file_name.replace('.mp3', '_raw.mp3')
    with open(raw_file_name, 'wb') as file:
        file.write(audio)

    logger.info(f"原始音频已保存到: {raw_file_name}, 大小: {len(audio)} 字节")

    # 添加音频文件诊断信息
    try:
        with open(raw_file_name, 'rb') as f:
            first_bytes = f.read(16)
            logger.info(f"音频文件头部字节: {first_bytes.hex()}")

            # 检查是否是有效的MP3文件头
            if first_bytes.startswith(b'\xff\xfb') or first_bytes.startswith(b'\xff\xf3') or first_bytes.startswith(b'\xff\xf2'):
                logger.info("检测到有效的MP3文件头")
            elif first_bytes.startswith(b'ID3'):
                logger.info("检测到ID3标签，可能是MP3文件")
            else:
                logger.warning(f"未检测到标准MP3文件头，可能需要特殊处理")
    except Exception as e:
        logger.warning(f"无法读取音频文件头部信息: {e}")

    # 验证并处理音频文件（基于MiniMax官方文档优化）
    success = False

    # 首先验证原始文件是否可用
    logger.info("验证MiniMax生成的原始MP3文件...")
    ffmpeg_test_command = [
        'ffmpeg',
        '-loglevel', 'error',
        '-hide_banner',
        '-i', raw_file_name,
        '-f', 'null',
        '-'
    ]

    test_result = subprocess.run(ffmpeg_test_command, capture_output=True, text=True)
    if test_result.returncode == 0:
        logger.info("原始MP3文件验证成功，直接使用")
        # 直接重命名原始文件，不需要重新编码
        os.rename(raw_file_name, file_name)
        success = True
    else:
        logger.warning(f"原始MP3文件验证失败: {test_result.stderr}")
        logger.info("尝试修复音频文件...")

        # 策略1: 温和的重新编码（保持原有参数）
        try:
            logger.info("尝试策略1: 温和重新编码（保持MiniMax参数）")
            ffmpeg_command = [
                'ffmpeg',
                '-loglevel', 'error',
                '-hide_banner',
                '-i', raw_file_name,
                '-acodec', 'copy',  # 尝试复制音频流，不重新编码
                '-y',
                file_name
            ]

            logger.info(f"FFmpeg命令: {' '.join(ffmpeg_command)}")
            subprocess.run(ffmpeg_command, capture_output=True, text=True, check=True)
            logger.info(f"策略1成功: 音频流复制完成")
            success = True

        except subprocess.CalledProcessError as e:
            logger.warning(f"策略1失败: {e.stderr}")

            # 策略2: 标准MP3重新编码（匹配MiniMax参数）
            try:
                logger.info("尝试策略2: 标准重新编码（匹配MiniMax设置）")
                ffmpeg_command = [
                    'ffmpeg',
                    '-loglevel', 'error',
                    '-hide_banner',
                    '-i', raw_file_name,
                    '-acodec', 'libmp3lame',
                    '-ar', '44100',  # 匹配MiniMax的sample_rate
                    '-ab', '128k',   # 匹配MiniMax的bitrate
                    '-ac', '1',      # 匹配MiniMax的channel
                    '-y',
                    file_name
                ]

                logger.info(f"FFmpeg命令: {' '.join(ffmpeg_command)}")
                subprocess.run(ffmpeg_command, capture_output=True, text=True, check=True)
                logger.info(f"策略2成功: 音频重新编码完成")
                success = True

            except subprocess.CalledProcessError as e2:
                logger.warning(f"策略2失败: {e2.stderr}")

                # 策略3: 最宽松的重新编码
                try:
                    logger.info("尝试策略3: 宽松重新编码（忽略格式错误）")
                    ffmpeg_command = [
                        'ffmpeg',
                        '-loglevel', 'error',
                        '-hide_banner',
                        '-err_detect', 'ignore_err',
                        '-i', raw_file_name,
                        '-acodec', 'libmp3lame',
                        '-ar', '44100',
                        '-ab', '128k',
                        '-ac', '1',
                        '-y',
                        file_name
                    ]

                    logger.info(f"FFmpeg命令: {' '.join(ffmpeg_command)}")
                    subprocess.run(ffmpeg_command, capture_output=True, text=True, check=True)
                    logger.info(f"策略3成功: 音频重新编码完成")
                    success = True

                except subprocess.CalledProcessError as e3:
                    logger.error(f"策略3失败: {e3.stderr}")
                    logger.error("所有音频处理策略都失败了")

    # 处理结果
    if success:
        # 删除原始文件（如果还存在）
        if os.path.exists(raw_file_name):
            os.remove(raw_file_name)
            logger.info(f"已删除原始文件: {raw_file_name}")
    else:
        # 如果所有策略都失败，使用原始文件
        logger.warning("所有音频处理策略都失败，使用原始音频文件")
        if os.path.exists(raw_file_name):
            os.rename(raw_file_name, file_name)
            logger.warning(f"使用原始音频文件: {file_name}")

    logger.info(f"Audio saved to {file_name}")
    return file_name

if __name__ == "__main__":
    minimax_request("这是一个示例音频","test61")