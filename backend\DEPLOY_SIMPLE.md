# 在Fly.io上部署FastAPI + Celery + Redis应用（简化版）

本文档提供了在Fly.io上部署FastAPI应用和Celery Worker的简化指南。

## 前提条件

1. 安装Fly.io CLI工具：
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```

2. 登录Fly.io：
   ```bash
   flyctl auth login
   ```

## 部署步骤

### 1. 创建Fly.io应用

如果你还没有创建应用，可以使用以下命令创建：

```bash
# 创建API应用
flyctl apps create aipresenter-api

# 创建Worker应用
flyctl apps create aipresenter-worker
```

### 2. 创建共享存储卷

为了在多个实例之间共享文件，我们需要创建Fly.io卷：

```bash
# 为API应用创建卷
flyctl volumes create aipresenter_data --size 1 --app aipresenter-api

# 为Worker应用创建卷
flyctl volumes create aipresenter_data --size 1 --app aipresenter-worker
```

### 3. 设置环境变量

在部署前，使用`flyctl secrets set`命令设置所有必要的环境变量：

```bash
# 设置API应用的环境变量
flyctl secrets set --app aipresenter-api \
    SUPABASE_URL="你的Supabase URL" \
    SUPABASE_ANON_KEY="你的Supabase匿名密钥" \
    SUPABASE_SERVICE_ROLE_KEY="你的Supabase服务角色密钥" \
    GROUP_ID="你的Minimax组ID" \
    MINIMAX_APIKEY="你的Minimax API密钥" \
    PDF_UPLOAD_FOLDER="/app/data/pdf_upload" \
    SCRIPT_UPLOAD_FOLDER="/app/data/script_upload" \
    RESULT_FOLDER="/app/data/results"

# 设置Worker应用的环境变量
flyctl secrets set --app aipresenter-worker \
    SUPABASE_URL="你的Supabase URL" \
    SUPABASE_ANON_KEY="你的Supabase匿名密钥" \
    SUPABASE_SERVICE_ROLE_KEY="你的Supabase服务角色密钥" \
    GROUP_ID="你的Minimax组ID" \
    MINIMAX_APIKEY="你的Minimax API密钥" \
    PDF_UPLOAD_FOLDER="/app/data/pdf_upload" \
    SCRIPT_UPLOAD_FOLDER="/app/data/script_upload" \
    RESULT_FOLDER="/app/data/results"
```

### 4. 部署应用

直接使用`flyctl deploy`命令部署应用，明确指定配置文件和应用名称：

```bash
# 部署API应用
cd backend
fly deploy --config fly.api.toml --app aipresenter-api

# 部署Worker应用
cd backend
fly deploy --config fly.worker.toml --app aipresenter-worker
```

Redis URL已经在`fly.toml`文件中设置，其他环境变量通过Fly.io的secrets设置。

### 5. 验证部署

检查应用状态：

```bash
# 检查API应用状态
flyctl status --app aipresenter-api

# 检查Worker应用日志
flyctl logs --app aipresenter-api
```

## 环境变量管理

你可以使用`flyctl secrets list`命令查看已设置的环境变量：

```bash
flyctl secrets list --app aipresenter-api
flyctl secrets list --app aipresenter-worker
```

如果需要更新环境变量，可以再次使用`flyctl secrets set`命令。

## 故障排除

### 1. 检查日志

如果应用无法启动或运行异常，请检查日志：

```bash
flyctl logs --app aipresenter-api
flyctl logs --app aipresenter-worker
```

### 2. 检查环境变量

确保所有必要的环境变量都已设置：

```bash
flyctl secrets list --app aipresenter-api
flyctl secrets list --app aipresenter-worker
```

### 3. 检查Redis连接

确保Redis连接正常：

```bash
# 进入应用控制台
flyctl console --app aipresenter-api
#在应用控制台检查环境变量
printenv

# 在控制台中测试Redis连接
python -c "import redis; r = redis.from_url('redis://default:<EMAIL>:6379'); print(r.ping())"
```

## 更新应用

要更新应用，只需重新部署：

```bash
cd backend
flyctl deploy --config fly.api.toml --app aipresenter-api
flyctl deploy --config fly.worker.toml --app aipresenter-worker
```
