{"name": "ai-presenter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aboutbits/react-ui": "^2.11.5", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-tabs": "^1.1.9", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@vercel/speed-insights": "^1.2.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^10.18.0", "lucide-react": "^0.292.0", "next": "^14.2.28", "next-themes": "^0.2.1", "ogl": "^1.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-h5-audio-player": "^3.10.0-rc.1", "styled-components": "^6.1.18", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17"}}