"use client"

import { ArrowDown } from "lucide-react"
import { Animate } from "@/components/animations/animate"
import { EnhancedButton } from "@/components/ui/enhanced-button"
import { motion } from "framer-motion"
import BlurText from "@/components/animations/BlurText"
import Aurora from '@/components/animations/Aurora'

export function HeroSection() {
  const scrollToForm = () => {
    const formElement = document.querySelector("#processing-form")
    if (formElement) {
      // 获取元素位置
      const elementPosition = formElement.getBoundingClientRect().top + window.pageYOffset
      // 添加偏移量，使滚动位置稍微靠上（这里设置为100px的偏移）
      const offsetPosition = elementPosition - 100

      // 使用 window.scrollTo 而不是 scrollIntoView，以便添加偏移量
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })
    }
  }

  return (
    <div className="relative overflow-hidden" id="home">
      <div className="absolute inset-0 z-0">
      <Aurora
        colorStops={["#3A29FF", "#FF94B4", "#FF3232"]}
        blend={0.5}
        amplitude={1.0}
        speed={0.5}
      />
      </div>
      <div className="container relative mx-auto px-4 py-20 md:py-32">
        <div className="flex flex-col items-center text-center">
          <Animate type="fade" delay={0.1}>
            <motion.div
              className="inline-flex items-center rounded-full subtle-glass px-3 py-1 text-sm mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <span className="flex h-2 w-2 rounded-full bg-theme-blue mr-2 animate-subtle-pulse"></span>
              <span>AI-Powered Presentation Voiceover</span>
            </motion.div>
          </Animate>

          {/* <Animate type="slide" delay={0.2}>
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6 gradient-text">AI Presenter</h1>
          </Animate> */}

          {/* <Animate type="fade" delay={0.3}>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mb-8">
              Create professional voiceovers for your presentations using advanced AI technology
            </p>
          </Animate> */}

          <BlurText
            text="AI Presenter"
            delay={150}
            animateBy="words"
            direction="top"
            className="text-2xl mb-6 text-4xl md:text-6xl font-bold"
          />
          <BlurText
            text="Create professional voiceovers for your presentations using advanced AI technology"
            delay={100}
            stepDuration={0.5}
            animateBy="words"
            direction="top"
            className="text-xl md:text-2xl text-muted-foreground max-w-3xl mb-8 text-center"
          />

          <Animate type="slide" delay={0.4}>
            <div className="flex flex-col sm:flex-row gap-4">
              <EnhancedButton
                size="lg"
                color="blue"
                onClick={scrollToForm}
                className="px-8 bg-white text-black hover:bg-white/90"
              >
                Get Started
              </EnhancedButton>
              <EnhancedButton
                size="lg"
                variant="outline"
                color="purple"
                onClick={scrollToForm}
                className="px-8 border-white/10 dark:text-white text-black hover:text-black dark:hover:text-white hover:border-black/20 dark:hover:border-white/20 subtle-glass"
              >
                Learn More
              </EnhancedButton>
            </div>
          </Animate>

          <Animate type="fade" delay={0.5}>
            <motion.div
              className="mt-16"
              animate={{ y: [0, -5, 0] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
            >
              <EnhancedButton
                variant="ghost"
                size="icon"
                onClick={scrollToForm}
                className="dark:text-white text-black hover:text-theme-blue dark:hover:bg-white/5 hover:bg-black/5 bg-transparent"
              >
                <ArrowDown className="h-6 w-6" />
              </EnhancedButton>
            </motion.div>
          </Animate>
        </div>
      </div>
    </div>
  )
}
