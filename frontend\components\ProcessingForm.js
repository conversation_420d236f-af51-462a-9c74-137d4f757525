// import { useState, useEffect } from 'react';
// import axios from 'axios';
// import styles from '../styles/ProcessingForm.module.css';

// const ProcessingForm = () => {
//   // 状态管理
//   const [pdfFile, setPdfFile] = useState(null);
//   const [txtFile, setTxtFile] = useState(null);
//   const [selectedVoice, setSelectedVoice] = useState('male-qn-qingse');
//   const [uploading, setUploading] = useState(false);
//   const [taskId, setTaskId] = useState(null);
//   const [taskStatus, setTaskStatus] = useState('idle');
//   const [taskResultUrl, setTaskResultUrl] = useState(null);
//   const [error, setError] = useState(null);

//   // 处理文件选择
//   const handlePdfChange = (e) => {
//     setPdfFile(e.target.files[0]);
//   };

//   const handleTxtChange = (e) => {
//     setTxtFile(e.target.files[0]);
//   };

//   // 处理音色选择
//   const handleVoiceChange = (e) => {
//     setSelectedVoice(e.target.value);
//   };

//   // 处理表单提交
//   const handleSubmit = async (e) => {
//     e.preventDefault();

//     // 客户端校验
//     if (!pdfFile) {
//       setError('请选择一个PDF文件');
//       return;
//     }

//     if (!txtFile) {
//       setError('请选择一个txt文件');
//       return;
//     }

//     // 重置状态
//     setUploading(true);
//     setError(null);
//     setTaskId(null);
//     setTaskStatus('idle');
//     setTaskResultUrl(null);

//     // 准备表单数据
//     const formData = new FormData();
//     formData.append('pdf_file', pdfFile);
//     if (txtFile) {
//       formData.append('txt_file', txtFile);
//     }
//     formData.append('voice_option', selectedVoice);

//     try {
//       // 发送请求到后端API
//       const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || 'http://localhost:8000';
//       console.log('Sending request to:', `${BACKEND_API_BASE_URL}/api/submit-job`);

//       const response = await axios.post(`${BACKEND_API_BASE_URL}/api/submit-job`, formData, {
//         headers: {
//           'Content-Type': 'multipart/form-data'
//         }
//       });

//       // 处理成功响应
//       console.log('任务提交成功:', response.data);
//       setTaskId(response.data.task_id);
//       setTaskStatus('queued');

//       // 上传成功消息现在显示在按钮中
//       console.log('上传成功:', response.data.message);
//     } catch (err) {
//       // 处理错误
//       console.error('提交任务失败:', err);
//       setError(err.response?.data?.detail || '提交任务失败，请重试');
//       setTaskStatus('failed');
//     } finally {
//       setUploading(false);
//     }
//   };

//   // 轮询任务状态
//   useEffect(() => {
//     let intervalId = null;

//     if (taskId && (taskStatus !== 'completed' && taskStatus !== 'failed')) {
//       // 设置轮询间隔
//       intervalId = setInterval(async () => {
//         try {
//           const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL;
//           const response = await axios.get(`${BACKEND_API_BASE_URL}/api/status/${taskId}`);

//           // 更新任务状态
//           setTaskStatus(response.data.status);

//           // 处理完成状态
//           if (response.data.status === 'completed') {
//             clearInterval(intervalId);
//             setTaskResultUrl(response.data.result_url);
//           }

//           // 处理失败状态
//           if (response.data.status === 'failed') {
//             clearInterval(intervalId);
//             setError(response.data.error_message || '处理失败，请重试');
//           }
//         } catch (err) {
//           // 处理轮询错误
//           console.error('获取任务状态失败:', err);
//           clearInterval(intervalId);
//           setError('获取任务状态失败，请刷新页面重试');
//           setTaskStatus('failed');
//         }
//       }, 3000); // 每3秒轮询一次
//     }

//     // 清理函数
//     return () => {
//       if (intervalId) {
//         clearInterval(intervalId);
//       }
//     };
//   }, [taskId, taskStatus]);

//   // 处理结果下载
//   const handleDownload = () => {
//     if (taskResultUrl) {
//       const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || 'http://localhost:8000';
//       const fullUrl = `${BACKEND_API_BASE_URL}${taskResultUrl}`;

//       // 创建一个临时链接并触发下载
//       const link = document.createElement('a');
//       link.href = fullUrl;

//       // 根据文件扩展名设置下载文件名
//       const isVideo = taskResultUrl.toLowerCase().endsWith('.mp4');
//       link.setAttribute('download', isVideo ? 'presentation.mp4' : 'voiceover.mp3');

//       document.body.appendChild(link);
//       link.click();
//       document.body.removeChild(link);
//     }
//   };

//   // 渲染结果部分
//   const renderResult = () => {
//     if (taskStatus === 'completed' && taskResultUrl) {
//       const BACKEND_API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_API_BASE_URL || 'http://localhost:8000';
//       const fullUrl = `${BACKEND_API_BASE_URL}${taskResultUrl}`;
//       const isVideo = taskResultUrl.toLowerCase().endsWith('.mp4');

//       return (
//         <div className={styles.resultContainer}>
//           <h3>处理完成！</h3>

//           {isVideo ? (
//             <video
//               controls
//               src={fullUrl}
//               className={styles.videoPlayer}
//               width="100%"
//               height="auto"
//             ></video>
//           ) : (
//             <audio
//               controls
//               src={fullUrl}
//               className={styles.audioPlayer}
//             ></audio>
//           )}

//           <button
//             onClick={handleDownload}
//             className={styles.downloadButton}
//           >
//             下载{isVideo ? '视频' : '音频'}文件
//           </button>
//         </div>
//       );
//     }
//     return null;
//   };

//   // 渲染状态指示器
//   const renderStatusIndicator = () => {
//     if (taskId && taskStatus) {
//       let statusText = '';
//       let statusClass = '';

//       switch (taskStatus) {
//         case 'queued':
//           statusText = '任务已排队，等待处理...';
//           statusClass = styles.statusQueued;
//           break;
//         case 'processing':
//           statusText = '正在处理您的文件...';
//           statusClass = styles.statusProcessing;
//           break;
//         case 'completed':
//           statusText = '处理完成！';
//           statusClass = styles.statusCompleted;
//           break;
//         case 'failed':
//           statusText = `处理失败: ${error || '未知错误'}`;
//           statusClass = styles.statusFailed;
//           break;
//         default:
//           statusText = '等待提交...';
//           statusClass = '';
//       }

//       return (
//         <div className={`${styles.statusIndicator} ${statusClass}`}>
//           {statusText}
//         </div>
//       );
//     }
//     return null;
//   };

//   return (
//     <div className={styles.container}>
//       <h2>PPT AI 配音服务</h2>

//       <form onSubmit={handleSubmit}>
//         <div className={styles.formGroup}>
//           <label htmlFor="pdfFile">上传PPT文件 (PDF格式) *</label>
//           <input
//             type="file"
//             id="pdfFile"
//             accept=".pdf"
//             onChange={handlePdfChange}
//             disabled={uploading || taskId !== null}
//           />
//         </div>

//         <div className={styles.formGroup}>
//           <label htmlFor="txtFile">上传脚本文件 (TXT格式)</label>
//           <input
//             type="file"
//             id="txtFile"
//             accept=".txt"
//             onChange={handleTxtChange}
//             disabled={uploading || taskId !== null}
//           />
//         </div>

//         <div className={styles.formGroup}>
//           <label htmlFor="voiceOption">选择配音音色</label>
//           <select
//             id="voiceOption"
//             value={selectedVoice}
//             onChange={handleVoiceChange}
//             disabled={uploading || taskId !== null}
//           >
//             <option value="male-qn-qingse">青涩青年</option>
//             <option value="male-qn-jingying">精英青年</option>
//             <option value="female-shaonv">少女音色</option>
//             <option value="female-yujie">御姐音色</option>
//           </select>
//         </div>

//         <button
//           type="submit"
//           className={`${styles.button} ${taskStatus === 'queued' ? styles.buttonSuccess : ''}`}
//           disabled={uploading || taskId !== null || !pdfFile}
//         >
//           {uploading ? '正在上传...' :
//            taskStatus === 'queued' ? '上传成功，正在处理...' :
//            taskStatus === 'processing' ? '正在处理...' :
//            taskStatus === 'completed' ? '处理完成' :
//            '开始处理'}
//         </button>
//       </form>

//       {/* 成功消息现在显示在按钮中 */}

//       {renderStatusIndicator()}

//       {renderResult()}

//       {/* 显示客户端校验错误 */}
//       {error && !taskId && <div className={styles.errorText}>{error}</div>}
//     </div>
//   );
// };

// export default ProcessingForm;
