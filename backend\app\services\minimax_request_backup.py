# 如果测试失败，使用这个代码恢复FFmpeg重新编码

def minimax_request_with_ffmpeg_reencoding(text, save_folder, custom_voice_setting=None, output_filename=None):
    """
    带FFmpeg重新编码的版本（备用）
    """
    import subprocess

    # 使用非流式请求
    audio = call_tts_non_stream(text, custom_voice_setting)

    # 检查音频数据是否为空
    if not audio or len(audio) == 0:
        logger.error("MiniMax API返回的音频数据为空")
        raise Exception("Failed to generate audio: empty response from MiniMax API")

    # 确保保存文件夹存在
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)

    # 生成文件名
    if output_filename:
        file_name = f'{save_folder}/{output_filename}.mp3'
    else:
        timestamp = int(time.time())
        file_name = f'{save_folder}/output_total_{timestamp}.mp3'

    # 保存原始音频文件
    raw_file_name = file_name.replace('.mp3', '_raw.mp3')
    with open(raw_file_name, 'wb') as file:
        file.write(audio)

    logger.info(f"Raw audio saved to {raw_file_name}, size: {len(audio)} bytes")

    # 验证并修复音频文件
    try:
        # 使用FFmpeg重新编码音频文件以确保兼容性
        ffmpeg_command = [
            'ffmpeg',
            '-loglevel', 'error',
            '-hide_banner',
            '-i', raw_file_name,
            '-acodec', 'mp3',
            '-ar', '44100',  # 标准采样率
            '-ab', '192k',   # 比特率
            '-ac', '1',      # 单声道
            '-y',            # 覆盖输出文件
            file_name
        ]

        logger.info(f"重新编码音频文件: {' '.join(ffmpeg_command)}")
        result = subprocess.run(ffmpeg_command, capture_output=True, text=True, check=True)
        logger.info(f"音频重新编码成功: {file_name}")

        # 删除原始文件
        os.remove(raw_file_name)

    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg重新编码失败: {e.stderr}")
        # 如果重新编码失败，使用原始文件
        os.rename(raw_file_name, file_name)
        logger.warning(f"使用原始音频文件: {file_name}")
    except Exception as e:
        logger.error(f"音频处理过程中发生错误: {str(e)}")
        # 如果处理失败，使用原始文件
        if os.path.exists(raw_file_name):
            os.rename(raw_file_name, file_name)

    logger.info(f"Audio saved to {file_name}")
    return file_name
