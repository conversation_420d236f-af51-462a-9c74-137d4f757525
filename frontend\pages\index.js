import Head from 'next/head';
import { Head<PERSON> } from "../components/header"
import { AIVoiceoverService } from "../components/ai-voiceover-service"
import { Features } from "../components/features"
import { Footer } from "../components/footer"
import { HomeHeader } from "../components/home-header"
import { AnimationProvider } from "../components/animations/animation-provider"
import { SubtleBackground } from "../components/subtle-background"

export default function HomePage() {
  return (
    <>
      <Head>
        <title>AI Presenter | Smart Presentation Voiceover Assistant</title>
        <meta name="description" content="Create professional voiceovers for your presentations using AI technology" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <AnimationProvider>
        <div className="min-h-screen flex flex-col">
          <SubtleBackground />
          <Header />
          <main className="flex-1">
            <HomeHeader />
            <div className="container mx-auto px-4 py-12 md:py-24">
              <Features />
              <div className="mt-16 max-w-3xl mx-auto">
                <AIVoiceoverService />
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </AnimationProvider>
    </>
  );
}










