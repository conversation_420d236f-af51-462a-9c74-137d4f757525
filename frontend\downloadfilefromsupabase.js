import { createClient } from '@supabase/supabase-js'

// Create a single supabase client for interacting with your database
const supabase = createClient(
    'https://mvdkqcttssakkuqxnqtk.supabase.co', 
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im12ZGtxY3R0c3Nha2t1cXhucXRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYxNjEyMzAsImV4cCI6MjA2MTczNzIzMH0.TZID0xA_7iFS9JIorcRuVcGc0SDzEckK4D4sVuMQ_S8')

const { data, error } = await supabase
  .storage
  .from('result-dubbing')
  .list('folder', {
    limit: 100,
    offset: 0,
  })


console.log(data, error)
// console.log(data1, error1)