"use client"

import * as React from "react"
import { cva } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"

const enhancedButtonVariants = cva("relative overflow-hidden transition-all", {
  variants: {
    variant: {
      default: "flow-border glow-effect",
      outline: "flow-border",
      ghost: "",
      link: "",
    },
    color: {
      default: "",
      blue: "glow-effect",
      purple: "glow-effect purple",
      teal: "glow-effect teal",
    },
    size: {
      default: "",
      sm: "",
      lg: "",
      icon: "",
    },
  },
  defaultVariants: {
    variant: "default",
    color: "default",
    size: "default",
  },
})

const EnhancedButton = React.forwardRef(
  ({ className, variant, color, size, loading, children, ...props }, ref) => {
    return (
      <Button
        className={cn(enhancedButtonVariants({ variant, color, size }), className)}
        ref={ref}
        disabled={loading || props.disabled}
        {...props}
      >
        {loading ? (
          <div className="flex items-center">
            <svg
              className="animate-rotate-loader -ml-1 mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Loading...
          </div>
        ) : (
          children
        )}
      </Button>
    )
  },
)
EnhancedButton.displayName = "EnhancedButton"

export { EnhancedButton }
