import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../components/auth/auth-provider';
import { Header } from '../components/header';
import { Footer } from '../components/footer';
import { SubtleBackground } from '../components/subtle-background';

export default function DashboardPage() {
  const { user, loading, signOut, supabase } = useAuth();
  const router = useRouter();

  // New: Quota related state
  const [quota, setQuota] = useState(null);
  const [quotaLoading, setQuotaLoading] = useState(true);
  const [quotaError, setQuotaError] = useState(null);

  // Redirect to login page if user is not logged in
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  // Fetch quota
  useEffect(() => {
    if (!loading && user) {
      setQuotaLoading(true);
      setQuotaError(null);
      supabase
        .from('user_details')
        .select('free_quota')
        .eq('id', user.id)
        .single()
        .then(({ data, error }) => {
          if (error) {
            setQuotaError('Failed to fetch quota');
            setQuota(null);
          } else {
            setQuota(data?.free_quota ?? 0);
          }
        })
        .catch(() => {
          setQuotaError('Failed to fetch quota');
          setQuota(null);
        })
        .finally(() => setQuotaLoading(false));
    }
  }, [loading, user, supabase]);

  // Show loading state if loading or user is not logged in
  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Loading...</h2>
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Dashboard | AI Presenter</title>
        <meta name="description" content="AI Presenter User Dashboard" />
      </Head>

      <div className="min-h-screen flex flex-col">
        <SubtleBackground />
        <Header />
        <main className="flex-1 container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <div className="bg-card border border-border rounded-lg p-6 shadow-lg">
              <h1 className="text-2xl font-bold mb-6">Welcome back, {user.email}</h1>
              
              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Your Account Information</h2>
                <div className="bg-background p-4 rounded-md">
                  <p><strong>User ID:</strong> {user.id}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Last Sign In:</strong> {new Date(user.last_sign_in_at).toLocaleString()}</p>
                  <p><strong>Remaining Free Voiceovers:</strong> {quotaLoading ? 'Loading...' : quotaError ? <span className="text-red-500">{quotaError}</span> : quota}</p>
                </div>
              </div>
              
              <div className="mb-8">
                <h2 className="text-xl font-semibold mb-4">Recent Projects</h2>
                <div className="bg-background p-4 rounded-md text-center">
                  <p className="text-muted-foreground">You haven't created any projects yet</p>
                  <button 
                    className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
                    onClick={() => router.push('/')}
                  >
                    Create New Project
                  </button>
                </div>
              </div>
              
              <button
                onClick={signOut}
                className="px-4 py-2 border border-destructive text-destructive rounded-md hover:bg-destructive/10 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}
