import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { createClient } from '@supabase/supabase-js';

// 初始化 Supabase 客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function AuthCallbackPage() {
  const router = useRouter();
  const [message, setMessage] = useState('Processing your authentication...');

  useEffect(() => {
    // 处理认证回调
    const handleAuthCallback = async () => {
      try {
        // 检查 URL 中是否有 hash 或 code 参数
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const queryParams = router.query;

        // 处理邮箱确认回调 (通常包含 hash 参数)
        if (window.location.hash) {
          // 获取当前会话状态
          const { data: { session } } = await supabase.auth.getSession();

          if (session) {
            // 如果已经有会话，直接重定向到首页
            console.log('Session found after email confirmation');
            router.push('/');
            return;
          }

          // 如果没有会话但有 hash，可能是 Supabase 已经处理了 token
          // 尝试刷新页面以获取会话
          const { error } = await supabase.auth.refreshSession();

          if (error) {
            console.error('Error refreshing session:', error);
            router.push('/login?error=session_refresh_error');
            return;
          }

          router.push('/');
          return;
        }

        // 处理 OAuth 回调 (通常包含 code 参数)
        if (queryParams.code) {
          // 交换code获取会话
          const { data, error } = await supabase.auth.exchangeCodeForSession(queryParams.code);

          if (error) {
            console.error('Error exchanging code for session:', error);
            router.push('/login?error=auth_callback_error');
            return;
          }

          // 获取当前会话和用户信息
          const { data: { session } } = await supabase.auth.getSession();

          if (session && session.user) {
            // 检查用户详情是否已存在
            setMessage('Checking user profile...');
            const { data: userDetail, error: fetchError } = await supabase
              .from('user_detail')
              .select('*')
              .eq('id', session.user.id)
              .single();

            if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 是"没有找到结果"的错误
              console.error('Error fetching user details:', fetchError);
            }

            // 如果用户详情不存在，创建一个新的
            if (!userDetail) {
              setMessage('Setting up your account...');

              // 从用户邮箱或提供商信息中获取用户名
              let userName = '';
              if (session.user.email) {
                userName = session.user.email.split('@')[0];
              } else if (session.user.user_metadata && session.user.user_metadata.full_name) {
                userName = session.user.user_metadata.full_name;
              } else {
                userName = `user_${Math.floor(Math.random() * 10000)}`;
              }

              // 插入用户详情记录
              const { error: insertError } = await supabase
                .from('user_detail')
                .insert([
                  {
                    id: session.user.id,
                    username: userName,
                    // free_quota 已设置默认值为 3，不需要显式指定
                  }
                ]);

              if (insertError) {
                console.error('Error creating user details:', insertError);
                // 即使创建失败，我们仍然允许用户继续
              } else {
                console.log('User details created successfully');
              }
            }
          }

          router.push('/');
          return;
        }

        // 如果没有 hash 或 code 参数，重定向到登录页面
        if (!window.location.hash && !queryParams.code) {
          console.log('No auth parameters found in URL');
          router.push('/login');
        }
      } catch (error) {
        console.error('Error in auth callback:', error);
        router.push('/login?error=unexpected_error');
      }
    };

    // 当路由准备好后执行回调处理
    if (router.isReady) {
      handleAuthCallback();
    }
  }, [router.isReady, router.query, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-4">{message}</h2>
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
      </div>
    </div>
  );
}
