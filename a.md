你好！请根据以下总结，帮助我完善一个使用 React/Next.js (前端)、Supabase (认证、数据库、RLS) 和 FastAPI (自定义后端) 的项目的认证和授权功能。我们已经完成了基础的项目搭建和 Supabase 初始化。

核心目标：

确保用户身份通过 Supabase 进行认证。
对需要登录才能使用的功能和访问的数据进行授权控制，且授权判断主要在后端执行。
实现一个具体的授权示例：限制用户免费生成配音的次数。
技术栈分工总结：

根据我们之前的讨论，各部分的角色和职责如下：

Supabase Authentication Service (后端服务，由 Supabase 提供):
职责： 唯一负责用户的注册、登录、密码管理、第三方登录 (如 Google Auth) 等认证流程。安全地存储用户凭据并签发 JWT (JSON Web Token) 作为用户身份的凭证。
Supabase Database (PostgreSQL，后端服务，由 Supabase 提供):
职责： 存储应用的所有数据，包括与用户关联的业务数据（如用户配置、配音记录、配额计数等）。
关键：Row Level Security (RLS) 在数据库层面提供第一层授权保护。它根据 JWT 中的用户身份 (auth.uid(), auth.role() 等) 控制哪些用户可以访问/修改数据库中的哪些行和列。
React/Next.js 前端:
职责：
使用 Supabase JavaScript 客户端库 (@supabase/supabase-js 或 @supabase/auth-helpers-nextjs) 处理认证流程的 UI (登录、注册、退出按钮和表单)。
通过 Supabase 客户端获取用户的会话信息和 JWT。
调用 Supabase 客户端方法安全地从数据库获取用户相关数据用于渲染 UI (例如显示用户昵称、剩余配音次数)，但这些读取操作的权限由 RLS 强制控制。
当用户尝试执行需要授权的操作时 (例如点击“生成配音”按钮)，获取当前有效的 JWT。
发起 HTTP 请求到自定义的 FastAPI 后端接口，并将 JWT 放在 Authorization: Bearer <token> 请求头中。
处理 FastAPI 后端返回的响应，包括成功结果和各种错误状态码 (如 401 未认证、403 无权限)。
注意： 前端只根据认证状态和从后端安全获取的数据来优化用户体验和界面显示，不执行核心的安全授权判断。
FastAPI 后端:
职责：
处理前端发来的需要复杂业务逻辑或高级授权判断的请求 (例如生成配音)。
核心：验证前端传来的 Supabase JWT。这是确认请求者身份在后端的关键一步。需要使用 Supabase 的 JWT 公钥（或相关的 Secret，取决于使用的库和配置）来验证 Token 的签名和有效期。
从验证后的 JWT 中提取用户的唯一标识符 (UUID)。
执行业务逻辑层面的授权判断。这基于用户身份 (从 JWT 获取) 和应用特定的规则 (例如检查用户在数据库中的角色、订阅状态、资源所有权、本例中的配音次数配额)。
与 Supabase Database 交互（读取、写入数据），执行业务逻辑。后端通常使用服务角色密钥 (Service Role Key) 或其他后端安全方式连接数据库，以便在验证用户身份后，能够执行比前端 RLS 策略更广泛或特定的数据库操作（例如更新所有用户的某个字段，或者在验证用户身份后更新其 RLS 不允许直接更新的字段）。服务角色密钥安全性极高，绝对不能泄露到前端。
调用第三方服务 (如外部配音 API)。
返回相应的 HTTP 响应，包括成功结果和错误码 (401, 403 等)。
具体实现任务 (聚焦授权和配额例子):

请 Coding AI 侧重实现以下部分的代码：

Supabase Database Schema:


允许认证用户读取他们自己的配额记录。
React/Next.js 前端:

修改或创建配音功能组件。
在组件加载后，如果用户已登录，使用 supabase.auth.getUser() 获取用户 ID。
使用 supabase.from('user_details').select('free_quota').eq('id', userId).single() 安全地获取当前用户的已使用次数（RLS 会生效）。处理用户不存在配额记录的情况（视为 0 次）。
在 UI 上显示计算出的剩余次数 。
当用户点击生成配音按钮时，获取当前的 session.access_token (supabase.auth.getSession())。
使用 Workspace 或 axios 调用 FastAPI 后端的 /generate-voiceover 接口，将 access_token 放在 Authorization: Bearer ${accessToken} 请求头中。
处理接口的响应：成功时显示结果，失败时根据 FastAPI 返回的错误码 (特别是 403) 显示友好的错误信息（例如“免费次数已用完”）。
FastAPI 后端:

创建一个 /api/submit-job 的 POST 接口。
实现一个 FastAPI 依赖项 (Dependency)，该依赖项负责：
从请求头的 Authorization: Bearer <token> 中提取 JWT。
验证 JWT 的签名和有效期（使用 Supabase 的 JWT Secret 或公钥进行验证）。如果验证失败，抛出 HTTPException(401)。
从验证通过的 JWT 中提取用户 ID (payload.get("sub"))。
将提取到的用户 ID 作为依赖项提供给 /generate-voiceover 路由处理函数。
在 /generate-voiceover 路由处理函数中：
使用获取到的 user_id。
使用 Supabase Python 客户端（使用服务角色密钥初始化）或适当的数据库访问方式，查询 user_quotas 表，获取该用户的 free_voiceovers_used 次数。处理用户记录不存在的情况（视为 0 次）。
执行配额判断： 检查 free_quota = 0。
如果 free_voiceovers_used = 0，返回 HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="您的免费配音次数已用完。")。
如果 free_voiceovers_used >0,则：
更新该用户的 free_voiceovers_used 字段，将其值减一。使用 Upsert 操作可以简化插入或更新的逻辑。
调用实际的配音生成服务。
返回成功响应，包含配音结果（例如 URL）。
安全性注意： 确保 FastAPI 后端用来连接 Supabase 的服务角色密钥被妥善地存储在环境变量中，绝不硬编码或暴露。JWT Secret 也应安全存储。
Coding AI，请根据以上分工和任务，生成相关的代码骨架和关键逻辑实现。 请重点关注前端如何获取和发送 JWT，以及 FastAPI 后端如何接收、验证 JWT 并基于数据库中的配额数据进行授权判断和更新。数据库的 RLS 策略也要给出示例。

在实现过程中，如果遇到不明确的地方或需要进一步的细节（例如 JWT 验证库的选择、更复杂的 RLS 策略、错误处理的细节），请随时提出问题。



