import os
import json
import requests
import logging
from typing import Optional, Dict, Tuple, Union, BinaryIO, TYPE_CHECKING
from pathlib import Path
from dotenv import load_dotenv

# 条件导入，避免循环导入问题
if TYPE_CHECKING:
    from fastapi import UploadFile

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

'''
MiniMax音色克隆API封装

支持上传的文件需遵从以下规范：
- 上传的音频文件格式需为：mp3、m4a、wav格式
- 上传的音频文件的时长最少应不低于10秒，最长应不超过5分钟
- 上传的音频文件大小需不超过20mb

返回参数:
    input_sensitive  bool       输入音频是否命中风控
    demo_audio       string     如果请求体中传入了试听文本text以及试听模型model，那么本参数将以链接形式返回试听音频
    base_resp        BaseResp   状态码以及状态详情
'''

def clone_voice(
    audio_file: Union[str, Path, BinaryIO, 'UploadFile'],  # 添加UploadFile类型
    preview_text: str,
    voice_id: str = None,
    model: str = "speech-02-hd"
) -> Tuple[bool, Optional[str], Dict]:
    """
    克隆音色并生成试听音频

    Args:
        audio_file: 音频文件路径、已打开的文件对象或FastAPI的UploadFile对象
        preview_text: 试听文本
        voice_id: 音色ID，如果不提供则自动生成
        model: 使用的语音模型，默认为"speech-02-hd"

    Returns:
        Tuple[bool, Optional[str], Dict]:
            - 成功标志
            - 试听音频URL (如果成功)
            - 完整响应数据
    """
    # 加载环境变量
    load_dotenv()

    # 获取API凭证
    group_id = os.getenv("GROUP_ID")
    api_key = os.getenv("MINIMAX_APIKEY")

    if not group_id or not api_key:
        raise ValueError("环境变量GROUP_ID或MINIMAX_APIKEY未设置")

    # 如果未提供voice_id，生成一个随机ID
    if not voice_id:
        import uuid
        voice_id = f"voice_{uuid.uuid4().hex[:8]}"

    # 处理文件上传
    try:
        # 检查是否是FastAPI的UploadFile对象
        if hasattr(audio_file, 'file') and hasattr(audio_file, 'filename'):
            logger.info(f"检测到UploadFile对象，文件名: {audio_file.filename}")
            # 对于UploadFile对象，我们使用其file属性
            file_id = _upload_audio_file(audio_file, group_id, api_key)
        else:
            # 对于其他类型的输入，直接传递
            file_id = _upload_audio_file(audio_file, group_id, api_key)

        if not file_id:
            return False, None, {"error": "文件上传失败"}

        # 调用音色克隆API
        demo_audio_url = _clone_voice_api(file_id, voice_id, preview_text, model, group_id, api_key)

        if demo_audio_url:
            return True, demo_audio_url, {"voice_id": voice_id, "file_id": file_id}
        else:
            return False, None, {"error": "音色克隆失败"}

    except Exception as e:
        logger.error(f"音色克隆过程中发生错误: {str(e)}")
        return False, None, {"error": str(e)}

def _upload_fastapi_file(upload_file, url, headers, data) -> Optional[int]:
    """处理FastAPI的UploadFile对象并上传到MiniMax"""
    try:
        filename = upload_file.filename
        file_content = upload_file.file

        # 确定MIME类型
        mime_type = 'audio/mp3' if filename.lower().endswith('.mp3') else \
                    'audio/wav' if filename.lower().endswith('.wav') else \
                    'audio/mp4'  # .m4a文件通常使用audio/mp4 MIME类型

        logger.info(f"上传FastAPI文件: {filename}, MIME类型: {mime_type}")

        # 使用元组格式指定文件名和MIME类型
        files = {'file': (filename, file_content, mime_type)}
        response = requests.post(url, headers=headers, data=data, files=files)

        # 处理响应
        try:
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"上传响应: {response_data}")

                # 检查响应中是否包含file字段
                if "file" not in response_data:
                    logger.error(f"响应中缺少file字段: {response_data}")
                    return None

                # 检查file字段是否为None
                if response_data["file"] is None:
                    logger.error("响应中file字段为None")
                    return None

                # 检查file_id是否存在
                file_id = response_data["file"].get("file_id")
                if file_id is None:
                    logger.error(f"响应中缺少file_id字段: {response_data['file']}")
                    return None

                logger.info(f"文件上传成功，file_id: {file_id}")
                return file_id
            else:
                logger.error(f"文件上传失败，状态码: {response.status_code}, 响应: {response.text}")
                return None
        except Exception as e:
            logger.error(f"处理上传响应时发生错误: {str(e)}")
            return None
    except Exception as e:
        logger.error(f"上传FastAPI文件时发生错误: {str(e)}")
        return None

def _upload_audio_file(audio_file, group_id, api_key) -> Optional[int]:
    """上传音频文件到MiniMax"""
    url = f'https://api.minimax.chat/v1/files/upload?GroupId={group_id}'
    headers = {
        'authority': 'api.minimax.chat',
        'Authorization': f'Bearer {api_key}'
    }

    data = {
        'purpose': 'voice_clone'
    }

    # 检查是否是FastAPI的UploadFile对象
    if hasattr(audio_file, 'file') and hasattr(audio_file, 'filename'):
        logger.info(f"处理UploadFile对象: {audio_file.filename}")
        # 使用UploadFile对象的file属性和filename属性
        return _upload_fastapi_file(audio_file, url, headers, data)

    # 处理不同类型的文件输入
    if isinstance(audio_file, (str, Path)):
        # 文件路径
        file_path = str(audio_file)
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 获取文件名
        filename = os.path.basename(file_path)

        with open(file_path, 'rb') as f:
            # 明确指定文件名，确保API能识别文件类型
            files = {'file': (filename, f, 'audio/mp3' if filename.endswith('.mp3') else
                                        'audio/wav' if filename.endswith('.wav') else
                                        'audio/mp4')}  # .m4a文件通常使用audio/mp4 MIME类型
            logger.info(f"上传文件: {filename}, MIME类型: {files['file'][2]}")
            response = requests.post(url, headers=headers, data=data, files=files)
    else:
        # 已打开的文件对象 - 这种情况下我们需要获取文件名
        # 尝试从文件对象获取文件名，如果没有则使用默认名称
        try:
            # 检查是否是FastAPI的UploadFile对象
            if hasattr(audio_file, 'filename'):
                filename = audio_file.filename
            else:
                # 如果是普通文件对象，尝试获取名称
                filename = getattr(audio_file, 'name', 'voice_sample.mp3')

            # 确定MIME类型
            mime_type = 'audio/mp3' if filename.lower().endswith('.mp3') else \
                        'audio/wav' if filename.lower().endswith('.wav') else \
                        'audio/mp4'  # .m4a文件通常使用audio/mp4 MIME类型

            logger.info(f"上传文件: {filename}, MIME类型: {mime_type}")

            # 使用元组格式指定文件名和MIME类型
            files = {'file': (filename, audio_file, mime_type)}
            response = requests.post(url, headers=headers, data=data, files=files)
        except Exception as e:
            logger.error(f"处理文件对象时出错: {str(e)}")
            # 退回到简单方式
            files = {'file': audio_file}
            response = requests.post(url, headers=headers, data=data, files=files)

    try:
        if response.status_code == 200:
            response_data = response.json()
            logger.info(f"上传响应: {response_data}")

            # 检查响应中是否包含file字段
            if "file" not in response_data:
                logger.error(f"响应中缺少file字段: {response_data}")
                return None

            # 检查file字段是否为None
            if response_data["file"] is None:
                logger.error("响应中file字段为None")
                return None

            # 检查file_id是否存在
            file_id = response_data["file"].get("file_id")
            if file_id is None:
                logger.error(f"响应中缺少file_id字段: {response_data['file']}")
                return None

            logger.info(f"文件上传成功，file_id: {file_id}")
            return file_id
        else:
            logger.error(f"文件上传失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
    except Exception as e:
        logger.error(f"处理上传响应时发生错误: {str(e)}")
        return None

def _clone_voice_api(file_id, voice_id, text, model, group_id, api_key) -> Optional[str]:
    """调用MiniMax音色克隆API"""
    url = f'https://api.minimax.chat/v1/voice_clone?GroupId={group_id}'

    payload = json.dumps({
        "file_id": file_id,
        "voice_id": voice_id,
        "text": text,
        "model": model
    })

    headers = {
        'Authorization': f'Bearer {api_key}',
        'content-type': 'application/json'
    }

    response = requests.post(url, headers=headers, data=payload)

    try:
        if response.status_code == 200:
            response_data = response.json()
            logger.info(f"克隆响应: {response_data}")

            # 检查响应中是否包含demo_audio字段
            demo_audio = response_data.get("demo_audio")

            if demo_audio:
                logger.info(f"音色克隆成功，试听URL: {demo_audio}")
                return demo_audio
            else:
                logger.warning(f"音色克隆成功但未返回试听URL，完整响应: {response_data}")
                return None
        else:
            logger.error(f"音色克隆失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
    except Exception as e:
        logger.error(f"处理克隆响应时发生错误: {str(e)}")
        return None

# 示例用法
if __name__ == "__main__":
    # 测试音色克隆
    file_path = 'for_voice_clone.m4a'

    preview_text = '''
    各位评委老师大家好，这是我今天的ppt演讲！
    我论文的题目是《基于融合模型对电商平台用户流失因素分析》。
    在电子商务领域，平台的用户流失一直是一个备受关注的课题，
    现如今电子商务领域的用户量接近饱和，各平台已经从以往的增量竞争发展至存量竞争，如何挽留住客户已经成为各大企业提升竞争力的重要方面。
    下一页是理论与方法对于用户流失的分析与判断，传统的方法有RFM模型，现在也有许多基于机器学习的用户流失，包括XGBoost、Random Forest等。
    '''

    success, demo_url, response_data = clone_voice(file_path, preview_text)

    if success and demo_url:
        print(f"音色克隆成功！")
        print(f"试听URL: {demo_url}")
        print(f"响应数据: {response_data}")
    else:
        print(f"音色克隆失败: {response_data.get('error', '未知错误')}")