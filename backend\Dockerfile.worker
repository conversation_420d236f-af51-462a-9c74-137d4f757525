FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libsm6 \
    libxrender1 \
    libfontconfig1 \
    libice6 \
    libglib2.0-0 \
    ffmpeg \
    libxvidcore4 \
    libx264-dev && \
    rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建必要的目录
RUN mkdir -p /app/data/pdf_upload /app/data/script_upload /app/data/results

# 复制应用代码
COPY app /app/app

# 创建一个空的.env文件，确保环境变量文件存在
RUN touch /app/.env

# 注意：实际的.env文件应该通过flyctl secrets set命令设置
# 或者在部署前手动创建

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PDF_UPLOAD_FOLDER=/app/data/pdf_upload
ENV SCRIPT_UPLOAD_FOLDER=/app/data/script_upload
ENV RESULT_FOLDER=/app/data/results

# 添加启动脚本
RUN echo '#!/bin/bash\n\
echo "Starting Celery worker..."\n\
cd /app\n\
python -m app.worker_health &\n\
python -m celery -A app.celery_worker worker --loglevel=info --concurrency=2\n\
' > /app/start_worker.sh && chmod +x /app/start_worker.sh

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["/app/start_worker.sh"]
