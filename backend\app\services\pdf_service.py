import PyPDF2


def count_pdf_pages(pdf_filepath) -> int:
    """
    计算PDF文件的页数
    返回: int - PDF页数
    """
    try:
        with open(pdf_filepath, "rb") as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            return len(pdf_reader.pages)
    except Exception as e:
        print(f"Failed to count PDF pages: {e}")
        return None


if __name__ == "__main__":
    pdf_filepath = "pdf_upload/example.pdf"
    page_count = count_pdf_pages(pdf_filepath)
    print(f"PDF page count: {page_count}")
