import re

def extract_and_validate_slides(script_text, pdf_page_count):
    """
    提取脚本中的幻灯片内容并验证与PDF页数是否一致
    返回: (bool, str, list) - (是否有效, 错误信息, 幻灯片内容列表)
    """
    # 检查脚本是否为空
    if not script_text or script_text.strip() == "":
        return False, "Script is empty", []
    
    # 使用正则表达式提取幻灯片内容
    slide_pattern = re.compile(r'<\s*[Ss]lide\s+\d+\s*>(.*?)(?=<\s*[Ss]lide\s+\d+\s*>|$)', re.DOTALL)
    slides = slide_pattern.findall(script_text)
    
    # 如果没有找到幻灯片标记
    if not slides:
        # 尝试按段落分割
        paragraphs = [p.strip() for p in script_text.split('\n\n') if p.strip()]
        if paragraphs:
            # 如果有段落，就使用段落作为幻灯片内容
            return True, "", paragraphs
        else:
            # 如果没有段落，就将整个脚本作为一个幻灯片
            return True, "", [script_text.strip()]
    
    # 如果找到了幻灯片标记，但数量与PDF页数不匹配
    if pdf_page_count is not None and len(slides) != pdf_page_count:
        return False, f"Slide count ({len(slides)}) does not match PDF page count ({pdf_page_count})", []
    
    # 清理幻灯片内容
    cleaned_slides = [slide.strip() for slide in slides]
    
    return True, "", cleaned_slides

if __name__ == "__main__":
    with open("script_upload/example.txt", "r", encoding="utf-8") as f:
        script_text = f.read()
    is_valid, error_msg, slide_contents = extract_and_validate_slides(script_text, 6)
    print(f"Valid: {is_valid}, Error: {error_msg}")
    if is_valid:
        print(f"Extracted {len(slide_contents)} slides")
        print(slide_contents)



