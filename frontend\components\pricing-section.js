"use client"

import { Animate } from "./animations/animate";
import { Enhanced<PERSON>utton } from "./ui/enhanced-button";
import { motion } from "framer-motion";
import { Check, X } from "lucide-react";
import Link from 'next/link';

// Pricing card component
export const PricingCard = ({
  title,
  price,
  description,
  features,
  buttonText,
  buttonLink,
  highlighted = false,
  delay = 0
}) => {
  return (
    <Animate type="slide" delay={delay}>
      <motion.div
        className={`rounded-xl p-5 md:p-6 flex flex-col h-full ${
          highlighted
            ? "subtle-glass border-2 border-theme-purple/30 shadow-lg shadow-theme-purple/10"
            : "subtle-glass border border-white/10"
        }`}
        whileHover={{ y: -5, transition: { duration: 0.2 } }}
      >
        <div className="mb-3">
          <h3 className="text-lg md:text-xl font-bold mb-1">{title}</h3>
          <p className="text-sm text-muted-foreground mb-3">{description}</p>
          <div className="text-3xl md:text-4xl font-bold mb-2">
            {price}
          </div>
        </div>

        <div className="flex-grow">
          <ul className="space-y-2 my-4 text-sm">
            {features.slice(0, 4).map((feature, index) => (
              <li key={index} className="flex items-start">
                {feature.included ? (
                  <Check className="h-4 w-4 text-theme-blue shrink-0 mr-2 mt-0.5" />
                ) : (
                  <X className="h-4 w-4 text-muted-foreground shrink-0 mr-2 mt-0.5" />
                )}
                <span className={feature.included ? "" : "text-muted-foreground"}>
                  {feature.text}
                </span>
              </li>
            ))}
          </ul>
        </div>

        <Link href={buttonLink} className="mt-auto">
          <EnhancedButton
            size="md"
            color={highlighted ? "purple" : "blue"}
            className={`w-full ${
              highlighted
                ? "bg-theme-purple text-white hover:bg-theme-purple/90"
                : "bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70"
            }`}
          >
            {buttonText}
          </EnhancedButton>
        </Link>
      </motion.div>
    </Animate>
  );
};

// Pricing plans data
export const defaultPricingPlans = [
  {
    title: "Basic plan",
    price: "$10",
    description: "Our most popular plan.",
    features: [
      { text: "Basic features", included: true },
      { text: "Users", included: true },
      { text: "Individual data", included: true },
      { text: "Support", included: true },
      { text: "Automated workflows", included: false },
      { text: "200+ integrations", included: false },
    ],
    buttonText: "Get Started",
    buttonLink: "/",
    highlighted: false,
    delay: 0.1
  },
  {
    title: "Business plan",
    price: "$20",
    description: "Best for growing teams.",
    features: [
      { text: "Basic features", included: true },
      { text: "Users", included: true },
      { text: "Individual data", included: true },
      { text: "Support", included: true },
      { text: "Automated workflows", included: true },
      { text: "200+ integrations", included: true },
    ],
    buttonText: "Choose Pro",
    buttonLink: "/",
    highlighted: true,
    delay: 0.2
  },
  {
    title: "Enterprise plan",
    price: "$40",
    description: "Best for large teams.",
    features: [
      { text: "Basic features", included: true },
      { text: "Users", included: true },
      { text: "Individual data", included: true },
      { text: "Support", included: true },
      { text: "Automated workflows", included: true },
      { text: "200+ integrations", included: true },
    ],
    buttonText: "Contact Us",
    buttonLink: "/",
    highlighted: false,
    delay: 0.3
  }
];

// FAQ component
export const PricingFAQ = ({ faqs }) => {
  return (
    <div className="mt-24 max-w-3xl mx-auto">
      <Animate type="fade">
        <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Frequently Asked Questions</h2>
      </Animate>

      <div className="space-y-6">
        {faqs.map((faq, index) => (
          <Animate key={index} type="slide" delay={0.1 * (index + 1)}>
            <div className="subtle-glass rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
              <p className="text-muted-foreground">{faq.answer}</p>
            </div>
          </Animate>
        ))}
      </div>
    </div>
  );
};

// Default FAQ data
export const defaultFAQs = [
  {
    question: "How do I choose the right plan?",
    answer: "Choose a plan based on your usage needs. If you're an individual user or small team, the Basic plan may be sufficient. If you need more features and processing capacity, consider the Professional plan. For large enterprises or special requirements, the Enterprise plan offers customized services."
  },
  {
    question: "Can I change my plan at any time?",
    answer: "Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades will take effect at the end of your current billing cycle."
  },
  {
    question: "How do I get a custom quote for the Enterprise plan?",
    answer: "Click the 'Contact Us' button under the Enterprise plan, fill in your requirements, and our sales team will contact you within 24 hours to provide a customized solution and quote."
  }
];

// Main pricing section component
export function PricingSection({
  pricingPlans = defaultPricingPlans,
  faqs = defaultFAQs,
  showFAQ = true
}) {
  return (
    <div className="container mx-auto px-4 py-12 md:py-16">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-5xl mx-auto">
        {pricingPlans.map((plan, index) => (
          <PricingCard key={index} {...plan} />
        ))}
      </div>

      {showFAQ && <PricingFAQ faqs={faqs} />}
    </div>
  );
}
