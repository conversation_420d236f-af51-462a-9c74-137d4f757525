import Head from 'next/head';
import { Header } from "../components/header";
import { Footer } from "../components/footer";
import { AnimationProvider } from "../components/animations/animation-provider";
import { SubtleBackground } from "../components/subtle-background";
import { Animate } from "../components/animations/animate";
import { motion } from "framer-motion";
import { PageHeader } from "../components/page-header";
import { PricingSection, defaultPricingPlans, defaultFAQs } from "../components/pricing-section";
import { PricingComparisonTable } from "../components/pricing-comparison-table";
import { FloatingElements } from "../components/animations/PricingAnimations";



// Pricing page component
export default function PricingPage() {

  return (
    <>
      <Head>
        <title>Pricing | AI Presenter</title>
        <meta name="description" content="AI Presenter pricing plans - Choose the best option for your needs" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <AnimationProvider>
        <div className="min-h-screen flex flex-col">
          <SubtleBackground />
          <Header />
          <main className="flex-1">
            {/* Pricing page header */}
            <PageHeader
              title="Choose the Perfect Plan for You"
              titleClassName="text-2xl mb-16 text-4xl md:text-5xl font-bold"
              height="550px"
            />

            {/* Pricing cards section */}
            <div className="mt-[-150px] md:mt-[-180px]">
              <PricingSection
                pricingPlans={defaultPricingPlans}
                faqs={defaultFAQs}
                showFAQ={false}
              />
            </div>

            {/* Price comparison table section */}
            <div className="container mx-auto px-4 py-8 md:py-12 relative">
              <div className="absolute inset-0 z-0">
                <FloatingElements
                  count={15}
                  colors={["#3A29FF", "#9C27B0", "#FF3232"]}
                  minOpacity={0.02}
                  maxOpacity={0.05}
                />
              </div>
              <div className="relative z-10 max-w-5xl mx-auto">
                <Animate type="fade">
                  <h2 className="text-xl md:text-2xl font-bold mb-8 text-center">Reporting and analytics</h2>
                </Animate>
                <PricingComparisonTable plans={defaultPricingPlans} />
              </div>
            </div>

            {/* FAQ section */}
            <div className="container mx-auto px-4 pb-24">
              <div className="mt-12 max-w-3xl mx-auto">
                <Animate type="fade">
                  <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Frequently Asked Questions</h2>
                </Animate>

                <div className="space-y-6">
                  {defaultFAQs.map((faq, index) => (
                    <Animate key={index} type="slide" delay={0.1 * (index + 1)}>
                      <div className="subtle-glass rounded-lg p-6">
                        <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </div>
                    </Animate>
                  ))}
                </div>
              </div>
            </div>
          </main>
          <Footer />
        </div>
      </AnimationProvider>
    </>
  );
}
