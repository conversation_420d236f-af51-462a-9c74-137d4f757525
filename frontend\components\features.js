"use client"

import { Mic, FileText, Video, Zap, Clock, <PERSON>rkles } from "lucide-react"
import { Animate } from "@/components/animations/animate"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

export function Features() {
  const features = [
    {
      icon: <Mic className="h-6 w-6" />,
      title: "Professional AI Voices",
      description: "Choose from a variety of natural-sounding AI voices for your presentations",
      color: "blue",
    },
    {
      icon: <FileText className="h-6 w-6" />,
      title: "Simple Script Format",
      description: "Use our easy slide-based script format to perfectly sync audio with your slides",
      color: "purple",
    },
    {
      icon: <Video className="h-6 w-6" />,
      title: "Video Generation",
      description: "Automatically create videos with your slides and AI voiceover",
      color: "teal",
    },
    {
      icon: <Zap className="h-6 w-6" />,
      title: "Fast Processing",
      description: "Get your voiceovers and videos quickly with our optimized processing",
      color: "blue",
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Time Saving",
      description: "Save hours of recording and editing time with automated voiceovers",
      color: "purple",
    },
    {
      icon: <Sparkles className="h-6 w-6" />,
      title: "High Quality",
      description: "Enjoy clear, professional-quality audio for your presentations",
      color: "teal",
    },
  ]

  return (
    <section className="py-12" id="features">
      <Animate type="fade">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">Key Features</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Our AI-powered platform makes creating professional presentation voiceovers simple and efficient
          </p>
        </div>
      </Animate>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature, index) => (
          <Animate key={feature.title} type="fade" delay={0.1 * index}>
            <FeatureCard feature={feature} />
          </Animate>
        ))}
      </div>
    </section>
  )
}

function FeatureCard({ feature }) {
  return (
    <motion.div
      className="border border-white/10 rounded-lg p-6 subtle-glass"
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      <div
        className={cn(
          "p-3 rounded-full w-fit mb-4",
          feature.color === "blue" && "bg-theme-blue/10",
          feature.color === "purple" && "bg-theme-purple/10",
          feature.color === "teal" && "bg-theme-teal/10"
        )}
      >
        <div
          className={cn(
            "text-white",
            feature.color === "blue" && "text-theme-blue",
            feature.color === "purple" && "text-theme-purple",
            feature.color === "teal" && "text-theme-teal"
          )}
        >
          {feature.icon}
        </div>
      </div>
      <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
      <p className="text-muted-foreground">{feature.description}</p>
    </motion.div>
  )
}
