"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"

// 浮动元素组件
export default function FloatingElements({ 
  count = 10, 
  colors = ["#3A29FF", "#FF94B4", "#FF3232"], 
  minSize = 10, 
  maxSize = 40,
  minOpacity = 0.05,
  maxOpacity = 0.15,
  minDuration = 15,
  maxDuration = 30,
  className = ""
}) {
  const [elements, setElements] = useState([])

  useEffect(() => {
    // 生成随机浮动元素
    const generateElements = () => {
      const newElements = []
      for (let i = 0; i < count; i++) {
        const size = Math.floor(Math.random() * (maxSize - minSize) + minSize)
        const opacity = Math.random() * (maxOpacity - minOpacity) + minOpacity
        const duration = Math.random() * (maxDuration - minDuration) + minDuration
        const delay = Math.random() * 5
        const color = colors[Math.floor(Math.random() * colors.length)]
        const shape = Math.random() > 0.5 ? "circle" : "square"
        
        // 随机位置
        const left = `${Math.random() * 100}%`
        const top = `${Math.random() * 100}%`
        
        newElements.push({
          id: i,
          size,
          opacity,
          duration,
          delay,
          color,
          shape,
          left,
          top
        })
      }
      setElements(newElements)
    }

    generateElements()
  }, [count, colors, minSize, maxSize, minOpacity, maxOpacity, minDuration, maxDuration])

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={{
            left: element.left,
            top: element.top,
            width: element.size,
            height: element.size,
            backgroundColor: element.color,
            opacity: element.opacity,
            borderRadius: element.shape === "circle" ? "50%" : "20%",
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, 15, 0],
            rotate: [0, 180, 0],
          }}
          transition={{
            duration: element.duration,
            delay: element.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  )
}

// 价格标签动画组件
export function AnimatedPriceTag({ 
  price, 
  currency = "¥", 
  period = "/月", 
  isCustom = false,
  className = ""
}) {
  return (
    <motion.div 
      className={`inline-flex items-end ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <motion.span 
        className="text-sm font-medium mr-1"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        {currency}
      </motion.span>
      <motion.span 
        className="text-4xl md:text-5xl font-bold"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3, type: "spring" }}
      >
        {price}
      </motion.span>
      {!isCustom && (
        <motion.span 
          className="text-sm text-muted-foreground ml-1 mb-1"
          initial={{ y: 5, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          {period}
        </motion.span>
      )}
    </motion.div>
  )
}

// 特性列表动画组件
export function AnimatedFeatureList({ features, delay = 0.5 }) {
  return (
    <ul className="space-y-3 my-6">
      {features.map((feature, index) => (
        <motion.li 
          key={index} 
          className="flex items-start"
          initial={{ x: -10, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: delay + (index * 0.1) }}
        >
          {feature.icon}
          <span className={feature.className || ""}>
            {feature.text}
          </span>
        </motion.li>
      ))}
    </ul>
  )
}

// 脉冲动画组件
export function PulseEffect({ 
  size = 200, 
  color = "rgba(58, 41, 255, 0.1)", 
  duration = 2.5,
  className = ""
}) {
  return (
    <div className={`relative ${className}`}>
      <motion.div
        className="absolute rounded-full"
        style={{
          width: size,
          height: size,
          backgroundColor: color,
          top: `calc(50% - ${size / 2}px)`,
          left: `calc(50% - ${size / 2}px)`,
        }}
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.1, 0.2, 0.1],
        }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  )
}
