"""
启动脚本 - 用于在生产环境中启动FastAPI和Celery Worker
"""
import os
import sys
import argparse
import subprocess
import time
import signal
import logging
from multiprocessing import Process

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("server.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 进程列表，用于优雅关闭
processes = []

def start_fastapi(host="0.0.0.0", port=8000, workers=4, reload=False):
    """启动FastAPI服务"""
    logger.info(f"启动FastAPI服务: host={host}, port={port}, workers={workers}, reload={reload}")
    
    cmd = [
        "uvicorn", 
        "main:app", 
        "--host", host, 
        "--port", str(port)
    ]
    
    if workers > 1:
        cmd.extend(["--workers", str(workers)])
    
    if reload:
        cmd.append("--reload")
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    # 启动FastAPI进程
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # 添加到进程列表
    processes.append(("FastAPI", process))
    
    logger.info(f"FastAPI服务已启动，PID: {process.pid}")
    return process

def start_celery_worker(concurrency=None, loglevel="info"):
    """启动Celery Worker"""
    logger.info(f"启动Celery Worker: concurrency={concurrency}, loglevel={loglevel}")
    
    cmd = ["celery", "-A", "celery_worker", "worker", "--loglevel", loglevel]
    
    if concurrency:
        cmd.extend(["--concurrency", str(concurrency)])
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    # 启动Celery Worker进程
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # 添加到进程列表
    processes.append(("Celery Worker", process))
    
    logger.info(f"Celery Worker已启动，PID: {process.pid}")
    return process

def start_flower(port=5555):
    """启动Flower监控"""
    logger.info(f"启动Flower监控: port={port}")
    
    cmd = ["celery", "-A", "celery_worker", "flower", "--port", str(port)]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    # 启动Flower进程
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # 添加到进程列表
    processes.append(("Flower", process))
    
    logger.info(f"Flower监控已启动，PID: {process.pid}")
    return process

def log_output(process_name, process):
    """记录进程输出到日志"""
    for line in iter(process.stdout.readline, ""):
        if line:
            logger.info(f"[{process_name}] {line.strip()}")

def start_log_threads():
    """启动日志线程"""
    import threading
    
    for name, process in processes:
        thread = threading.Thread(
            target=log_output,
            args=(name, process),
            daemon=True
        )
        thread.start()

def signal_handler(sig, frame):
    """信号处理函数，用于优雅关闭"""
    logger.info(f"接收到信号 {sig}，开始关闭服务...")
    
    # 关闭所有进程
    for name, process in processes:
        logger.info(f"关闭 {name} 进程 (PID: {process.pid})...")
        process.terminate()
    
    # 等待进程关闭
    for name, process in processes:
        try:
            process.wait(timeout=5)
            logger.info(f"{name} 进程已关闭")
        except subprocess.TimeoutExpired:
            logger.warning(f"{name} 进程未能在5秒内关闭，强制终止")
            process.kill()
    
    logger.info("所有服务已关闭")
    sys.exit(0)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动FastAPI和Celery服务")
    
    # FastAPI参数
    parser.add_argument("--host", default="0.0.0.0", help="FastAPI主机地址")
    parser.add_argument("--port", type=int, default=8000, help="FastAPI端口")
    parser.add_argument("--workers", type=int, default=1, help="FastAPI工作进程数")
    parser.add_argument("--reload", action="store_true", help="启用FastAPI热重载")
    
    # Celery参数
    parser.add_argument("--celery-concurrency", type=int, help="Celery并发工作进程数")
    parser.add_argument("--celery-loglevel", default="info", help="Celery日志级别")
    
    # Flower参数
    parser.add_argument("--flower", action="store_true", help="启动Flower监控")
    parser.add_argument("--flower-port", type=int, default=5555, help="Flower端口")
    
    # 解析参数
    args = parser.parse_args()
    
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动Celery Worker
        start_celery_worker(
            concurrency=args.celery_concurrency,
            loglevel=args.celery_loglevel
        )
        
        # 启动Flower监控（如果需要）
        if args.flower:
            start_flower(port=args.flower_port)
        
        # 启动FastAPI
        start_fastapi(
            host=args.host,
            port=args.port,
            workers=args.workers,
            reload=args.reload
        )
        
        # 启动日志线程
        start_log_threads()
        
        logger.info("所有服务已启动")
        
        # 保持主进程运行
        while True:
            # 检查进程状态
            for i, (name, process) in enumerate(processes):
                if process.poll() is not None:
                    logger.error(f"{name} 进程已退出，返回码: {process.returncode}")
                    # 可以在这里添加重启逻辑
            
            time.sleep(5)
            
    except KeyboardInterrupt:
        logger.info("接收到键盘中断，关闭服务...")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logger.error(f"发生错误: {str(e)}", exc_info=True)
        signal_handler(signal.SIGTERM, None)

if __name__ == "__main__":
    main()
