#!/bin/bash
# 部署脚本 - 用于将应用部署到Fly.io

# 确保脚本在错误时退出
set -e

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help             显示帮助信息"
    echo "  -a, --api-only         仅部署API应用"
    echo "  -w, --worker-only      仅部署Worker应用"
    echo "  -r, --redis-url URL    设置Redis URL (默认使用Fly.io Redis服务)"
    echo "  -s, --setup-volumes    创建并设置Fly.io卷"
    echo "  -e, --env-file FILE    从文件加载环境变量"
    echo ""
    echo "示例:"
    echo "  $0                     部署API和Worker应用"
    echo "  $0 -a                  仅部署API应用"
    echo "  $0 -w                  仅部署Worker应用"
    echo "  $0 -r redis://user:pass@host:port  使用自定义Redis URL"
    echo "  $0 -s                  创建并设置Fly.io卷"
    echo "  $0 -e .env.production  从.env.production加载环境变量"
}

# 默认值
DEPLOY_API=true
DEPLOY_WORKER=true
REDIS_URL=""
SETUP_VOLUMES=false
ENV_FILE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -a|--api-only)
            DEPLOY_API=true
            DEPLOY_WORKER=false
            shift
            ;;
        -w|--worker-only)
            DEPLOY_API=false
            DEPLOY_WORKER=true
            shift
            ;;
        -r|--redis-url)
            REDIS_URL="$2"
            shift 2
            ;;
        -s|--setup-volumes)
            SETUP_VOLUMES=true
            shift
            ;;
        -e|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 加载环境变量
if [[ -n "$ENV_FILE" && -f "$ENV_FILE" ]]; then
    echo "从 $ENV_FILE 加载环境变量..."
    export $(grep -v '^#' "$ENV_FILE" | xargs)
fi

# 创建并设置卷
if $SETUP_VOLUMES; then
    echo "创建Fly.io卷..."
    flyctl volumes create aipresenter_data --size 1 --app aipresenter-api
    flyctl volumes create aipresenter_data --size 1 --app aipresenter-worker
fi

# 设置Redis URL
if [[ -z "$REDIS_URL" ]]; then
    echo "未提供Redis URL，请输入Redis URL:"
    read -p "Redis URL: " REDIS_URL
fi

# 从.env文件读取环境变量
load_env_file() {
    local env_file=".env"
    if [[ -n "$ENV_FILE" ]]; then
        env_file="$ENV_FILE"
    fi

    if [[ -f "$env_file" ]]; then
        echo "从 $env_file 读取环境变量..."

        # 读取环境变量
        if [[ -z "$SUPABASE_URL" ]]; then
            SUPABASE_URL=$(grep SUPABASE_URL "$env_file" | cut -d '=' -f2)
        fi

        if [[ -z "$SUPABASE_ANON_KEY" ]]; then
            SUPABASE_ANON_KEY=$(grep SUPABASE_ANON_KEY "$env_file" | cut -d '=' -f2)
        fi

        if [[ -z "$SUPABASE_SERVICE_ROLE_KEY" ]]; then
            SUPABASE_SERVICE_ROLE_KEY=$(grep SUPABASE_SERVICE_ROLE_KEY "$env_file" | cut -d '=' -f2)
        fi

        if [[ -z "$GROUP_ID" ]]; then
            GROUP_ID=$(grep GROUP_ID "$env_file" | cut -d '=' -f2)
        fi

        if [[ -z "$MINIMAX_APIKEY" ]]; then
            MINIMAX_APIKEY=$(grep MINIMAX_APIKEY "$env_file" | cut -d '=' -f2)
        fi
    else
        echo "警告: $env_file 文件不存在，将使用命令行提供的环境变量"
    fi
}

# 加载环境变量
load_env_file

# 部署API应用
if $DEPLOY_API; then
    echo "部署API应用..."

    # 创建包含所有环境变量的临时文件
    ENV_TEMP_FILE=$(mktemp)
    echo "REDIS_URL=$REDIS_URL" > "$ENV_TEMP_FILE"
    echo "SUPABASE_URL=$SUPABASE_URL" >> "$ENV_TEMP_FILE"
    echo "SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY" >> "$ENV_TEMP_FILE"
    echo "SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY" >> "$ENV_TEMP_FILE"
    echo "GROUP_ID=$GROUP_ID" >> "$ENV_TEMP_FILE"
    echo "MINIMAX_APIKEY=$MINIMAX_APIKEY" >> "$ENV_TEMP_FILE"

    # 设置环境变量
    echo "设置API应用环境变量..."
    flyctl secrets import --app aipresenter-api < "$ENV_TEMP_FILE"

    # 删除临时文件
    rm "$ENV_TEMP_FILE"

    # 部署应用
    echo "部署API应用..."
    flyctl deploy --config fly.api.toml
fi

# 部署Worker应用
if $DEPLOY_WORKER; then
    echo "部署Worker应用..."

    # 创建包含所有环境变量的临时文件
    ENV_TEMP_FILE=$(mktemp)
    echo "REDIS_URL=$REDIS_URL" > "$ENV_TEMP_FILE"
    echo "SUPABASE_URL=$SUPABASE_URL" >> "$ENV_TEMP_FILE"
    echo "SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY" >> "$ENV_TEMP_FILE"
    echo "SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY" >> "$ENV_TEMP_FILE"
    echo "GROUP_ID=$GROUP_ID" >> "$ENV_TEMP_FILE"
    echo "MINIMAX_APIKEY=$MINIMAX_APIKEY" >> "$ENV_TEMP_FILE"

    # 设置环境变量
    echo "设置Worker应用环境变量..."
    flyctl secrets import --app aipresenter-worker < "$ENV_TEMP_FILE"

    # 删除临时文件
    rm "$ENV_TEMP_FILE"

    # 部署应用
    echo "部署Worker应用..."
    flyctl deploy --config fly.worker.toml
fi

echo "部署完成!"
