@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;

    /* Theme colors */
    --theme-blue: 210 100% 50%;
    --theme-purple: 260 100% 60%;
    --theme-teal: 180 100% 40%;

    /* Glow colors */
    --glow-color: 210, 100%, 50%;

    /* Flow border colors */
    --border-start-color: rgba(33, 150, 243, 0.5);
    --border-mid-color: rgba(156, 39, 176, 0.5);
    --border-end-color: rgba(0, 188, 212, 0.5);

    /* Theme transition */
    --theme-transition: 0.3s ease;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    /* Theme colors */
    --theme-blue: 210 100% 60%;
    --theme-purple: 260 100% 70%;
    --theme-teal: 180 100% 50%;

    /* Glow colors */
    --glow-color: 210, 100%, 60%;

    /* Flow border colors */
    --border-start-color: rgba(33, 150, 243, 0.7);
    --border-mid-color: rgba(156, 39, 176, 0.7);
    --border-end-color: rgba(0, 188, 212, 0.7);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    transition: background-color var(--theme-transition), color var(--theme-transition);
  }
}

/* Subtle glass effect */
.subtle-glass {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color var(--theme-transition), border-color var(--theme-transition);
}

.light .subtle-glass {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Subtle border glow */
.subtle-border-glow {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: box-shadow var(--theme-transition);
}

.light .subtle-border-glow {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* Subtle hover effect */
.subtle-hover {
  transition: all 0.2s ease;
}

.subtle-hover:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.light .subtle-hover:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: background var(--theme-transition);
}

.light .gradient-text {
  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Subtle grid background */
.subtle-grid {
  background-size: 30px 30px;
  transition: background-image var(--theme-transition);
}

.light .subtle-grid {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
}

/* Reveal animation classes */
.reveal {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.reveal.active {
  opacity: 1;
  transform: translateY(0);
}

/* Button shine effect */
.btn-shine {
  position: relative;
  overflow: hidden;
}

.btn-shine::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: left 0.5s ease;
}

.btn-shine:hover::after {
  left: 100%;
}

/* Dot pattern */
.dot-pattern {
  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  transition: background-image var(--theme-transition);
}

.light .dot-pattern {
  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);
}

/* Flow border effect */
.flow-border {
  position: relative;
  z-index: 0;
}

.flow-border::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(
    --bg-flow-border,
    linear-gradient(
      90deg,
      var(--border-start-color),
      var(--border-mid-color),
      var(--border-end-color),
      var(--border-start-color)
    )
  );
  border-radius: inherit;
  z-index: -1;
  background-size: 300% 100%;
  animation: border-flow 3s ease infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.flow-border:hover::before {
  opacity: 1;
}

/* Glow effect */
.glow-effect {
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  --glow-color: var(--theme-blue-rgb, 33, 150, 243);
  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);
}

.glow-effect.purple:hover {
  --glow-color: var(--theme-purple-rgb, 156, 39, 176);
}

.glow-effect.teal:hover {
  --glow-color: var(--theme-teal-rgb, 0, 188, 212);
}

/* Theme transition */
.theme-transition {
  transition: all var(--theme-transition);
}

/* Page transition */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s, transform 0.3s;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.3s, transform 0.3s;
}

/* Nav link with indicator */
.nav-link {
  position: relative;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: hsl(var(--theme-blue));
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

.light ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
}

.light ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* Animation for loading spinner */
@keyframes border-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes subtle-pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.animate-subtle-pulse {
  animation: subtle-pulse 2s ease-in-out infinite;
}

.animate-rotate-loader {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}







