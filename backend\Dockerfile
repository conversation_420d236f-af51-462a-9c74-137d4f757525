FROM python:3.10.17 AS builder

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1
WORKDIR /app


RUN python -m venv .venv
COPY requirements.txt ./
RUN .venv/bin/pip install -r requirements.txt
FROM python:3.10.17-slim
WORKDIR /app

# 安装 OpenCV 和视频处理所需的系统依赖库
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libsm6 \
    libxrender1 \
    libfontconfig1 \
    libice6 \
    libglib2.0-0 \
    ffmpeg \
    libxvidcore4 \
    libx264-164 && \
    rm -rf /var/lib/apt/lists/* 
    
COPY --from=builder /app/.venv .venv/
COPY . .    
CMD ["/app/.venv/bin/uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
