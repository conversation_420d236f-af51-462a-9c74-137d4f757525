# FastAPI + Celery + Redis 后台任务处理系统

本项目使用FastAPI作为Web框架，Celery作为分布式任务队列，Redis作为消息代理和结果后端，实现了高效的后台任务处理系统。

## 系统架构

```
前端 <-> FastAPI服务 <-> Redis <-> Celery Worker(s)
                          ^
                          |
                       Flower监控
```

## 主要特性

- **异步任务处理**：使用Celery处理长时间运行的任务，不阻塞Web服务器
- **任务状态跟踪**：实时跟踪任务进度和状态
- **错误处理和重试**：自动重试失败的任务
- **可扩展性**：支持水平扩展，可以部署多个Worker处理任务
- **监控**：使用Flower监控任务执行情况

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务

### 开发环境

1. 启动Redis服务器（需要先安装Redis）

```bash
redis-server
```

2. 启动Celery Worker

```bash
cd backend/app
celery -A celery_worker worker --loglevel=info
```

3. 启动FastAPI服务器

```bash
cd backend/app
uvicorn main:app --reload
```

4. (可选) 启动Flower监控

```bash
cd backend/app
celery -A celery_worker flower
```

### 生产环境

使用提供的启动脚本一键启动所有服务：

```bash
cd backend/app
python start_server.py --workers 4 --celery-concurrency 8 --flower
```

参数说明：
- `--workers`：FastAPI工作进程数
- `--celery-concurrency`：Celery工作进程数
- `--flower`：启动Flower监控

## 环境变量配置

创建`.env`文件，配置以下环境变量：

```
# Redis配置
REDIS_URL=redis://localhost:6379/0

# Supabase配置
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## API接口

### 提交任务

```
POST /api/submit-job
```

参数：
- `pdf_file`：PDF文件
- `txt_file`：文本脚本文件（可选）
- `voice_option`：语音选项

返回：
```json
{
  "task_id": "任务ID",
  "message": "Files uploaded successfully. Processing started.",
  "status": "queued"
}
```

### 查询任务状态

```
GET /api/status/{task_id}
```

返回：
```json
{
  "status": "queued|processing|completed|failed",
  "progress": 0-100,
  "result_url": "结果URL（仅当状态为completed时）",
  "supabase_url": "Supabase URL（仅当状态为completed时）",
  "error_message": "错误信息（仅当状态为failed时）"
}
```

## 任务处理流程

1. 客户端上传文件并提交任务
2. FastAPI服务器保存文件并创建Celery任务
3. Celery Worker处理任务并更新任务状态
4. 客户端轮询任务状态
5. 任务完成后，客户端获取结果URL

## 注意事项

- 确保Redis服务器已启动
- 生产环境建议使用Supervisor或systemd管理服务
- 使用Docker可以简化部署过程
- 定期清理临时文件和过期任务结果
