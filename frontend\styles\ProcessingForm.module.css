/* styles/ProcessingForm.module.css */.container {
  max-width: 600px;  margin: 40px auto;
  padding: 30px;  border: 1px solid #eee;
  border-radius: 8px;  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  background-color: #fff;  font-family: sans-serif;
}
.container h2 {  text-align: center;
  color: #333;  margin-bottom: 30px;
}
.formGroup {  margin-bottom: 20px;
}
.formGroup label {  display: block;
  margin-bottom: 8px;  font-weight: bold;
  color: #555;}
.formGroup input[type="file"],
.formGroup select {  display: block;
  width: 100%;  padding: 10px;
  border: 1px solid #ccc;  border-radius: 4px;
  box-sizing: border-box;}
.button {
  display: block;  width: 100%;
  padding: 12px;  background-color: #0070f3;
  color: white;  border: none;
  border-radius: 4px;  font-size: 16px;
  cursor: pointer;  transition: background-color 0.3s ease;
}
.button:hover:not(:disabled) {  background-color: #005bb5;
}
.button:disabled {  background-color: #ccc;
  cursor: not-allowed;
}
.buttonSuccess {
  background-color: #4caf50;
}
.buttonSuccess:hover:not(:disabled) {
  background-color: #388e3c;
}
.status {
  margin-top: 25px;  padding: 15px;
  border-radius: 4px;  text-align: center;
  font-weight: bold;}
.status.info {
  background-color: #e0f7fa;  color: #0070f3;
}
.status.success {  background-color: #e8f5e9;
  color: #4caf50;}
.status.error {
  background-color: #ffebee;  color: #f44336;
}
.status.processing {    background-color: #fff3e0;
    color: #ff9800;}
.errorText {
  color: #f44336;  margin-top: 10px;
  text-align: center;
}

.successMessage {
  color: #4caf50;
  margin: 15px 0;
  padding: 10px;
  background-color: #e8f5e9;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.resultContainer {
  margin-top: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  text-align: center;
}

.audioPlayer {
  width: 100%;
  margin: 15px 0;
}

.videoPlayer {
  width: 100%;
  max-height: 400px;
  margin: 15px 0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.downloadButton {
  padding: 10px 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.downloadButton:hover {
  background-color: #388e3c;
}

.statusIndicator {
  margin: 20px 0;
  padding: 12px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.statusQueued {
  background-color: #e3f2fd;
  color: #1976d2;
}

.statusProcessing {
  background-color: #fff3e0;
  color: #ff9800;
}

.statusCompleted {
  background-color: #e8f5e9;
  color: #4caf50;
}

.statusFailed {
  background-color: #ffebee;
  color: #f44336;
}















































