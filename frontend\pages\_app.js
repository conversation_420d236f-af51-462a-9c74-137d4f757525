import { Inter } from 'next/font/google';
import { ThemeProvider } from '../components/theme-provider';
import { AuthProvider } from '../components/auth/auth-provider';
import { SpeedInsights } from '@vercel/speed-insights/next';
import '../styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

function MyApp({ Component, pageProps }) {
  return (
    <>
      <style jsx global>{`
        body {
          font-family: ${inter.style.fontFamily};
        }
      `}</style>
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
        <AuthProvider>
          <Component {...pageProps} />
          <SpeedInsights />
        </AuthProvider>
      </ThemeProvider>
    </>
  );
}

export default MyApp;
