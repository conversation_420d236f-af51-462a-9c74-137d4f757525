"use client"

import { motion } from "framer-motion"
import BlurText from "@/components/animations/BlurText"
import Aurora from '@/components/animations/Aurora'

export function PageHeader({
  title,
  subtitle = null,
  titleClassName = "text-2xl mb-6 text-4xl md:text-6xl font-bold",
  subtitleClassName = "text-xl md:text-2xl text-muted-foreground max-w-3xl mb-8 text-center",
  height = "500px", // 默认高度
  children = null,
  colorStops = ["#3A29FF", "#FF94B4", "#FF3232"],
  blend = 0.5,
  amplitude = 1.0,
  speed = 0.5,
}) {
  return (
    <div className="relative overflow-hidden" style={{ height }}>
      <div className="absolute inset-0 z-0">
        <Aurora
          colorStops={colorStops}
          blend={blend}
          amplitude={amplitude}
          speed={speed}
        />
      </div>
      <div className="container relative mx-auto px-4 h-full flex items-center">
        <div className="flex flex-col items-center text-center max-w-3xl mx-auto w-full">
          {title && (
            <BlurText
              text={title}
              delay={150}
              animateBy="words"
              direction="top"
              className={titleClassName}
            />
          )}
          
          {subtitle && (
            <BlurText
              text={subtitle}
              delay={100}
              stepDuration={0.5}
              animateBy="words"
              direction="top"
              className={subtitleClassName}
            />
          )}
          
          {children}
        </div>
      </div>
    </div>
  )
}
