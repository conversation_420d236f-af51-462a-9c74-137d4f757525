"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"
import { Check, X } from "lucide-react"

// Floating elements component
export function FloatingElements({
  count = 10,
  colors = ["#3A29FF", "#FF94B4", "#FF3232"],
  minSize = 10,
  maxSize = 40,
  minOpacity = 0.05,
  maxOpacity = 0.15,
  minDuration = 15,
  maxDuration = 30,
  className = ""
}) {
  const [elements, setElements] = useState([])
  const [isMounted, setIsMounted] = useState(false)

  // 确保组件只在客户端渲染后执行
  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    if (!isMounted) return;

    // Generate random floating elements
    const generateElements = () => {
      const newElements = []
      for (let i = 0; i < count; i++) {
        const size = Math.floor(Math.random() * (maxSize - minSize) + minSize)
        const opacity = Math.random() * (maxOpacity - minOpacity) + minOpacity
        const duration = Math.random() * (maxDuration - minDuration) + minDuration
        const delay = Math.random() * 5
        const color = colors[Math.floor(Math.random() * colors.length)]
        const shape = Math.random() > 0.5 ? "circle" : "square"

        // Random position
        const left = `${Math.random() * 100}%`
        const top = `${Math.random() * 100}%`

        newElements.push({
          id: i,
          size,
          opacity,
          duration,
          delay,
          color,
          shape,
          left,
          top
        })
      }
      setElements(newElements)
    }

    generateElements()
  }, [count, colors, minSize, maxSize, minOpacity, maxOpacity, minDuration, maxDuration, isMounted])

  // 如果组件尚未挂载，返回一个空的容器
  if (!isMounted) {
    return <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}></div>
  }

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={{
            left: element.left,
            top: element.top,
            width: element.size,
            height: element.size,
            backgroundColor: element.color,
            opacity: element.opacity,
            borderRadius: element.shape === "circle" ? "50%" : "20%",
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, 15, 0],
            rotate: [0, 180, 0],
          }}
          transition={{
            duration: element.duration,
            delay: element.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  )
}

// Price tag animation component
export function AnimatedPriceTag({
  price,
  currency = "$",
  period = "/mo",
  isCustom = false,
  className = ""
}) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 如果组件尚未挂载，使用静态渲染
  if (!isMounted) {
    return (
      <div className={`inline-flex items-end ${className}`}>
        <span className="text-sm font-medium mr-1">{currency}</span>
        <span className="text-4xl md:text-5xl font-bold">{price}</span>
        {!isCustom && <span className="text-sm text-muted-foreground ml-1 mb-1">{period}</span>}
      </div>
    )
  }

  return (
    <div className={`inline-flex items-end ${className}`}>
      <span className="text-sm font-medium mr-1">{currency}</span>
      <span className="text-4xl md:text-5xl font-bold">{price}</span>
      {!isCustom && <span className="text-sm text-muted-foreground ml-1 mb-1">{period}</span>}
    </div>
  )
}

// Feature list animation component
export function AnimatedFeatureList({ features, delay = 0.5 }) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 如果组件尚未挂载，使用静态渲染
  if (!isMounted) {
    return (
      <ul className="space-y-3 my-6">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start">
            {feature.included ? (
              <Check className="h-5 w-5 text-theme-blue shrink-0 mr-2" />
            ) : (
              <X className="h-5 w-5 text-muted-foreground shrink-0 mr-2" />
            )}
            <span className={feature.included ? "" : "text-muted-foreground"}>
              {feature.text}
            </span>
          </li>
        ))}
      </ul>
    )
  }

  return (
    <ul className="space-y-3 my-6">
      {features.map((feature, index) => (
        <motion.li
          key={index}
          className="flex items-start"
          initial={{ x: -10, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: delay + (index * 0.1) }}
        >
          {feature.included ? (
            <Check className="h-5 w-5 text-theme-blue shrink-0 mr-2" />
          ) : (
            <X className="h-5 w-5 text-muted-foreground shrink-0 mr-2" />
          )}
          <span className={feature.included ? "" : "text-muted-foreground"}>
            {feature.text}
          </span>
        </motion.li>
      ))}
    </ul>
  )
}

// Pulse animation component
export function PulseEffect({
  size = 200,
  color = "rgba(58, 41, 255, 0.1)",
  duration = 2.5,
  className = ""
}) {
  return (
    <div className={`relative ${className}`}>
      <motion.div
        className="absolute rounded-full"
        style={{
          width: size,
          height: size,
          backgroundColor: color,
          top: `calc(50% - ${size / 2}px)`,
          left: `calc(50% - ${size / 2}px)`,
        }}
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.1, 0.2, 0.1],
        }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    </div>
  )
}

// Comparison table row animation component
export function AnimatedTableRow({ children, index = 0 }) {
  return (
    <motion.tr
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 * index, duration: 0.5 }}
      className="border-b border-white/10"
    >
      {children}
    </motion.tr>
  )
}

// Comparison table cell animation component
export function AnimatedTableCell({ children, highlight = false }) {
  return (
    <motion.td
      className={`p-4 text-center ${highlight ? "bg-theme-purple/10" : ""}`}
      whileHover={{ backgroundColor: highlight ? "rgba(156, 39, 176, 0.2)" : "rgba(255, 255, 255, 0.05)" }}
      transition={{ duration: 0.2 }}
    >
      {children}
    </motion.td>
  )
}
