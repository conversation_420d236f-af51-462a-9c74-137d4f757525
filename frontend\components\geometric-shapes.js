"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

export function GeometricShapes({ className }) {
  const shapes = [
    {
      type: "circle",
      size: "h-16 w-16",
      position: "top-20 left-10",
      color: "bg-theme-blue/20",
      animation: {
        y: [0, -15, 0],
        transition: { duration: 4, repeat: Infinity, ease: "easeInOut" },
      },
    },
    {
      type: "square",
      size: "h-20 w-20",
      position: "top-40 right-20",
      color: "bg-theme-purple/20",
      animation: {
        rotate: [0, 45, 0],
        transition: { duration: 8, repeat: Infinity, ease: "easeInOut" },
      },
    },
    {
      type: "triangle",
      size: "h-24 w-24",
      position: "bottom-20 left-1/4",
      color: "border-theme-teal/30",
      animation: {
        scale: [1, 1.1, 1],
        transition: { duration: 6, repeat: Infinity, ease: "easeInOut" },
      },
    },
    {
      type: "donut",
      size: "h-32 w-32",
      position: "bottom-40 right-10",
      color: "border-theme-blue/20",
      animation: {
        rotate: [0, 360],
        transition: { duration: 20, repeat: Infinity, ease: "linear" },
      },
    },
  ]

  return (
    <div className={cn("fixed inset-0 pointer-events-none overflow-hidden -z-10", className)}>
      {shapes.map((shape, index) => (
        <motion.div
          key={index}
          className={cn("absolute opacity-50", shape.position, shape.size)}
          animate={shape.animation}
        >
          {shape.type === "circle" && <div className={cn("rounded-full w-full h-full", shape.color)} />}
          {shape.type === "square" && <div className={cn("rounded-md w-full h-full", shape.color)} />}
          {shape.type === "triangle" && (
            <div className="w-0 h-0 border-l-[50px] border-r-[50px] border-b-[86px] border-l-transparent border-r-transparent border-b-theme-teal/30" />
          )}
          {shape.type === "donut" && (
            <div className={cn("rounded-full w-full h-full border-8", shape.color)} />
          )}
        </motion.div>
      ))}
    </div>
  )
}
